import pytest
from loguru import logger
from common.utils import Utils
from apis.ton_api import TonApi


@pytest.mark.ton
class TestTon(TonApi):

    datas = TonApi.data

    #getAddressInformation
    @pytest.mark.parametrize('data', datas['ton']['getAddressInformation'])
    def test_getAddressInformation(self, env, data):
        result = self.getAddressInformation(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getExtendedAddressInformation
    @pytest.mark.parametrize('data', datas['ton']['getExtendedAddressInformation'])
    def test_getExtendedAddressInformation(self, env, data):
        result = self.getExtendedAddressInformation(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getWalletInformation
    @pytest.mark.parametrize('data', datas['ton']['getWalletInformation'])
    def test_getWalletInformation(self, env, data):
        result = self.getWalletInformation(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getTransactions
    @pytest.mark.parametrize('data', datas['ton']['getTransactions'])
    def test_getTransactions(self, env, data):
        result = self.getTransactions(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getAddressBalance
    @pytest.mark.parametrize('data', datas['ton']['getAddressBalance'])
    def test_getAddressBalance(self, env, data):
        result = self.getAddressBalance(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getAddressState
    @pytest.mark.parametrize('data', datas['ton']['getAddressState'])
    def test_getAddressState(self, env, data):
        result = self.getAddressState(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #packAddress
    @pytest.mark.parametrize('data', datas['ton']['packAddress'])
    def test_packAddress(self, env, data):
        result = self.packAddress(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #unpackAddress
    @pytest.mark.parametrize('data', datas['ton']['unpackAddress'])
    def test_unpackAddress(self, env, data):
        result = self.unpackAddress(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getTokenData
    @pytest.mark.parametrize('data', datas['ton']['getTokenData'])
    def test_getTokenData(self, env, data):
        result = self.getTokenData(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #detectAddress
    @pytest.mark.parametrize('data', datas['ton']['detectAddress'])
    def test_detectAddress(self, env, data):
        result = self.detectAddress(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getMasterchainInfo
    @pytest.mark.parametrize('data', datas['ton']['getMasterchainInfo'])
    def test_getMasterchainInfo(self, env, data):
        result = self.getMasterchainInfo(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    #getConsensusBlock
    @pytest.mark.parametrize('data', datas['ton']['getConsensusBlock'])
    def test_getConsensusBlock(self, env, data):
        result = self.getConsensusBlock(env['ton'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')
