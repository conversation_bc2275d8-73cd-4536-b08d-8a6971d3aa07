"""
EVM链通用测试基类
消除重复代码，提供统一的测试模板
"""
import pytest
from loguru import logger
from typing import Dict, Any, Optional
from common.utils import Utils
from apis.jsonrpc_api import JsonrpcApi


class BaseEvmTest(JsonrpcApi):
    """
    EVM兼容链的通用测试基类
    提供通用的fixture和测试方法模板
    """

    # 子类需要设置这个属性来指定链名
    CHAIN_NAME: str = None

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        if cls.CHAIN_NAME is None:
            raise ValueError(f"子类 {cls.__name__} 必须设置 CHAIN_NAME 属性")

    @property
    def chain_name(self) -> str:
        """获取当前测试的链名"""
        return self.CHAIN_NAME

    @property
    def test_data(self) -> Dict[str, Any]:
        """获取当前链的测试数据"""
        return self.data.get(self.chain_name, {})

    def get_chain_url(self, env: Dict[str, Any], node_type: str = "regular") -> str:
        """
        获取指定类型的链URL

        Args:
            env: 环境配置
            node_type: 节点类型 ('regular', 'archive')

        Returns:
            str: 链的URL
        """
        if node_type == "archive":
            return env.get('archive_node', {}).get(self.chain_name, "")
        return env.get(self.chain_name, "")

    def get_ws_url(self, env: Dict[str, Any], node_type: str = "regular") -> str:
        """
        获取WebSocket URL

        Args:
            env: 环境配置
            node_type: 节点类型 ('regular', 'archive')

        Returns:
            str: WebSocket URL
        """
        if node_type == "archive":
            return env.get('archive_node', {}).get('ws', {}).get(self.chain_name, "")
        return env.get('ws', {}).get(self.chain_name, "")

    # ============ 通用 Fixtures ============

    @pytest.fixture(scope="class")
    def get_latest_block_hash(self, env):
        """获取最新区块哈希"""
        latest_block = self.get_block(self.get_chain_url(env), 'latest')
        if latest_block is None or 'hash' not in latest_block:
            pytest.fail("Failed to get latest block or block hash is missing")
        return latest_block['hash']

    @pytest.fixture(scope="class")
    def get_latest_transaction_hash(self, env):
        """获取最新交易哈希"""
        latest_block = self.get_block(self.get_chain_url(env), 'latest')
        if latest_block is None or not latest_block.get('transactions'):
            pytest.skip("No transactions in the latest block or failed to get block")

        latest_transaction_hash = latest_block['transactions'][0]
        if isinstance(latest_transaction_hash, dict) and 'hash' in latest_transaction_hash:
            latest_transaction_hash = latest_transaction_hash['hash']
        elif not isinstance(latest_transaction_hash, str):
            pytest.fail(f"Unexpected transaction format in block: {latest_transaction_hash}")
        return latest_transaction_hash

    @pytest.fixture(scope="class")
    def get_new_block_filter_hash(self, env):
        """获取新的区块过滤器哈希"""
        new_block_filter_hash = self.get_new_block_filter(self.get_chain_url(env)).filter_id
        return new_block_filter_hash

    @pytest.fixture(scope="class")
    def block_offset_factory(self, env):
        """
        工厂fixture，返回一个可以计算特定偏移量区块号的函数
        """
        latest_block = self.get_block(self.get_chain_url(env), 'latest')
        latest_block_number = int(latest_block['number'], 16) if isinstance(latest_block['number'], str) and latest_block['number'].startswith('0x') else int(latest_block['number'])

        def _get_block_with_offset(offset: int):
            """根据最新的区块号计算指定偏移量之前的区块号"""
            if offset <= 0:
                raise ValueError("Offset must be positive")
            calculated_block_number = latest_block_number - offset
            if calculated_block_number < 0:
                calculated_block_number = 0
            return hex(calculated_block_number)

        return _get_block_with_offset

    # ============ 通用测试方法模板 ============

    def run_standard_test(self, env: Dict[str, Any], data: Dict[str, Any],
                         method_name: str, node_type: str = "regular",
                         use_template: bool = False, template_vars: Optional[Dict[str, str]] = None):
        """
        运行标准的RPC测试

        Args:
            env: 环境配置
            data: 测试数据
            method_name: 方法名
            node_type: 节点类型
            use_template: 是否使用模板变量
            template_vars: 模板变量字典
        """
        if node_type == "archive":
            logger.info('开始请求存档节点')

        # 处理模板变量
        if use_template and template_vars:
            data = Utils.handle_template(data, template_vars)

        # 获取API方法
        api_method = getattr(self, method_name)
        url = self.get_chain_url(env, node_type)

        # 执行API调用
        result = api_method(url, **data['payload'])

        # 执行断言
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())

        # 如果有期望结果，进行验证
        if 'expected' in data:
            if method_name in ['eth_getBalance']:
                Utils.assert_contains(result.json()['result'], data['expected'])
            elif method_name in ['eth_call']:
                Utils.assert_contains(result.json())
            else:
                Utils.assert_contains(result.json(), data['expected'])

        # 检查不应包含的内容
        if method_name not in ['eth_call', 'net_listening', 'net_version']:
            Utils.assert_not_contains(result.json())

        logger.info('用例通过！')

    def run_websocket_tests(self, env: Dict[str, Any], get_latest_block_hash: str,
                          get_latest_transaction_hash: str, node_type: str = "regular"):
        """
        运行WebSocket测试

        Args:
            env: 环境配置
            get_latest_block_hash: 最新区块哈希
            get_latest_transaction_hash: 最新交易哈希
            node_type: 节点类型
        """
        # 处理模板数据
        template_data = Utils.handle_template(
            self.data,
            {
                'blockhash': get_latest_block_hash,
                'transactionhash': get_latest_transaction_hash
            }
        )

        # 建立WebSocket连接
        ws_url = self.get_ws_url(env, node_type)
        ws = Utils.websocket_connection(ws_url)

        # 定义要测试的方法列表
        ws_test_methods = [
            'eth_chainId', 'eth_getBlockByNumber', 'eth_getBlockByHash',
            'eth_blockNumber', 'eth_gasPrice', 'eth_getBalance',
            'eth_getTransactionByHash', 'eth_getTransactionByBlockHashAndIndex',
            'eth_getTransactionByBlockNumberAndIndex', 'eth_getTransactionReceipt',
            'eth_getTransactionCount', 'eth_getBlockTransactionCountByHash',
            'eth_getBlockTransactionCountByNumber', 'eth_getLogs',
            'eth_call', 'eth_newFilter', 'eth_newBlockFilter',
            'eth_newPendingTransactionFilter'
        ]

        for method in ws_test_methods:
            if method in template_data.get(self.chain_name, {}):
                test_data = template_data[self.chain_name][method][0]
                result = Utils.send_websocket(ws, test_data['payload'])

                if method == 'eth_call':
                    Utils.assert_contains(result)
                else:
                    Utils.assert_contains(result, test_data['expected'])

                logger.info(f'{method} WebSocket测试通过！')

        # 关闭WebSocket连接
        ws.close()
        logger.info('WebSocket测试完成！')