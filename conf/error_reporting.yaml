# 错误报告配置文件
# 配置错误报告的详细程度和格式

error_reporting:
  # 基本配置
  enabled: true
  log_level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR
  
  # 请求/响应日志配置
  request_logging:
    enabled: true
    log_headers: true
    log_params: true
    log_json_data: true
    log_form_data: true
    sanitize_sensitive_data: true
    sensitive_headers:
      - "authorization"
      - "x-api-key"
      - "cookie"
      - "token"
      - "api-key"
  
  response_logging:
    enabled: true
    log_headers: true
    log_status_code: true
    log_response_time: true
    log_response_size: true
    max_response_length: 1000  # 最大响应内容长度
    log_json_pretty: true
  
  # 错误分类配置
  error_categories:
    network:
      error_types:
        - "ConnectionError"
        - "Timeout"
        - "HTTPError"
        - "ConnectTimeout"
        - "ReadTimeout"
      status_codes: []
      suggestions:
        - "检查网络连接是否正常"
        - "验证目标服务器是否可达"
        - "检查防火墙和代理设置"
        - "尝试增加请求超时时间"
    
    authentication:
      error_types: []
      status_codes: [401, 403]
      suggestions:
        - "检查API密钥是否正确"
        - "验证认证头是否正确设置"
        - "确认账户权限是否足够"
        - "检查token是否已过期"
    
    server_error:
      error_types: []
      status_codes: [500, 502, 503, 504]
      suggestions:
        - "服务器内部错误，请稍后重试"
        - "检查服务器状态和负载"
        - "联系API提供方确认服务状态"
        - "查看服务器日志获取更多信息"
    
    client_error:
      error_types: []
      status_codes: [400, 404, 422]
      suggestions:
        - "检查请求参数是否正确"
        - "验证请求格式是否符合API规范"
        - "确认请求的资源是否存在"
        - "检查请求数据的类型和格式"
    
    assertion_error:
      error_types: ["AssertionError", "EnhancedAssertionError"]
      status_codes: []
      suggestions:
        - "检查API响应格式是否符合预期"
        - "验证测试数据是否正确"
        - "确认API行为是否发生变化"
        - "检查测试环境配置"
    
    json_error:
      error_types: ["JSONDecodeError"]
      status_codes: []
      suggestions:
        - "检查响应内容是否为有效JSON"
        - "验证API是否返回了错误页面"
        - "确认Content-Type是否正确"
        - "检查响应是否被截断"
  
  # 断言错误配置
  assertion_errors:
    include_context: true
    include_stack_trace: true
    max_context_length: 500
    show_diff: true
    highlight_differences: true
  
  # 测试上下文配置
  test_context:
    auto_collect: true
    include_test_name: true
    include_module_name: true
    include_chain_name: true
    include_method_name: true
    include_environment: true
    include_timestamp: true
    include_execution_time: true
  
  # 报告格式配置
  report_format:
    use_colors: true
    use_emojis: true
    section_separators: true
    max_line_length: 80
    indent_size: 2
    
    # 颜色配置（ANSI颜色代码）
    colors:
      error: "\033[91m"      # 红色
      warning: "\033[93m"    # 黄色
      success: "\033[92m"    # 绿色
      info: "\033[94m"       # 蓝色
      debug: "\033[90m"      # 灰色
      reset: "\033[0m"       # 重置
    
    # 表情符号配置
    emojis:
      error: "❌"
      warning: "⚠️"
      success: "✅"
      info: "ℹ️"
      debug: "🔍"
      request: "📤"
      response: "📥"
      time: "⏱️"
      context: "📋"
      suggestion: "💡"
      stack_trace: "🔍"
  
  # 性能监控配置
  performance:
    track_request_time: true
    track_assertion_time: true
    track_total_test_time: true
    slow_request_threshold: 5.0  # 秒
    slow_assertion_threshold: 1.0  # 秒
    slow_test_threshold: 10.0  # 秒
  
  # 重试配置
  retry:
    enabled: true
    max_retries: 3
    initial_delay: 1.0  # 秒
    backoff_multiplier: 2.0
    retry_on_status_codes: [500, 502, 503, 504]
    retry_on_exceptions:
      - "ConnectionError"
      - "Timeout"
  
  # 输出配置
  output:
    console: true
    file: false
    file_path: "logs/error_reports.log"
    json_format: false
    include_request_id: true
  
  # 调试配置
  debug:
    save_failed_requests: true
    save_responses: true
    save_path: "debug/failed_tests"
    include_full_stack_trace: true
    include_environment_info: true

# 特定链的配置覆盖
chain_specific:
  ethereum:
    performance:
      slow_request_threshold: 10.0  # 以太坊可能较慢
  
  solana:
    error_categories:
      solana_specific:
        error_types: ["SolanaRpcError"]
        suggestions:
          - "检查Solana节点状态"
          - "验证程序ID是否正确"
  
  starknet:
    request_logging:
      max_response_length: 2000  # Starknet响应可能较长

# 环境特定配置
environment_specific:
  development:
    error_reporting:
      log_level: "DEBUG"
      debug:
        save_failed_requests: true
        include_full_stack_trace: true
  
  testing:
    error_reporting:
      log_level: "INFO"
      output:
        console: true
        file: true
  
  production:
    error_reporting:
      log_level: "WARNING"
      request_logging:
        log_headers: false
        sanitize_sensitive_data: true
      debug:
        save_failed_requests: false
