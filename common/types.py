"""
类型定义模块
定义项目中使用的通用类型和类型别名
"""

from typing import (
    Dict, List, Any, Optional, Union, Callable, TypeVar, Generic,
    Protocol, runtime_checkable, Literal, TypedDict, NamedTuple
)
from typing_extensions import NotRequired
import httpx
from pathlib import Path

# 基础类型别名
JsonValue = Union[str, int, float, bool, None, Dict[str, Any], List[Any]]
JsonDict = Dict[str, JsonValue]
JsonList = List[JsonValue]

# 路径类型
PathLike = Union[str, Path]

# HTTP相关类型
HttpMethod = Literal['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
HttpHeaders = Dict[str, str]
HttpParams = Dict[str, Union[str, int, float, bool]]
HttpResponse = httpx.Response

# 配置相关类型
ConfigDict = Dict[str, Any]
EnvironmentName = Literal['alphanet', 'testnet', 'mainnet', 'development', 'production']

# 测试相关类型
TestData = Dict[str, Any]
TestCaseData = Dict[str, List[TestData]]
ChainName = str
MethodName = str

# RPC相关类型
RpcId = Union[str, int]
RpcParams = Union[List[JsonValue], Dict[str, JsonValue], None]

class RpcRequest(TypedDict):
    """JSON-RPC 请求结构"""
    jsonrpc: str
    method: str
    params: NotRequired[RpcParams]
    id: NotRequired[RpcId]

class RpcResponse(TypedDict):
    """JSON-RPC 响应结构"""
    jsonrpc: str
    id: NotRequired[RpcId]
    result: NotRequired[JsonValue]
    error: NotRequired[Dict[str, Any]]

class RpcError(TypedDict):
    """JSON-RPC 错误结构"""
    code: int
    message: str
    data: NotRequired[JsonValue]

# HTTP请求载荷类型
class HttpPayload(TypedDict):
    """HTTP请求载荷结构"""
    url: str
    method: HttpMethod
    headers: NotRequired[HttpHeaders]
    params: NotRequired[HttpParams]
    json: NotRequired[JsonDict]
    data: NotRequired[Union[str, bytes, Dict[str, Any]]]
    timeout: NotRequired[float]
    verify: NotRequired[bool]

# 测试用例结构
class TestCase(TypedDict):
    """单个测试用例结构"""
    payload: RpcRequest
    expected: JsonValue
    description: NotRequired[str]
    tags: NotRequired[List[str]]

# 错误报告相关类型
class ErrorContext(TypedDict):
    """错误上下文信息"""
    test_name: NotRequired[str]
    chain_name: NotRequired[str]
    method_name: NotRequired[str]
    environment: NotRequired[str]
    timestamp: NotRequired[str]
    execution_time: NotRequired[float]

ErrorCategory = Literal[
    'network', 'authentication', 'server', 'client', 
    'assertion', 'json', 'validation', 'unknown'
]

# 配置结构类型
class UrlConfig(TypedDict):
    """URL配置结构"""
    base_url: str
    endpoints: NotRequired[Dict[str, str]]
    timeout: NotRequired[float]
    verify_ssl: NotRequired[bool]

class ChainConfig(TypedDict):
    """链配置结构"""
    name: str
    type: str
    rpc_url: str
    chain_id: NotRequired[Union[str, int]]
    symbol: NotRequired[str]
    explorer: NotRequired[str]

# 协议定义
@runtime_checkable
class Configurable(Protocol):
    """可配置对象协议"""
    
    def reload_config(self) -> None:
        """重新加载配置"""
        ...
    
    def get_config(self, key: str) -> Any:
        """获取配置值"""
        ...

@runtime_checkable
class HttpClient(Protocol):
    """HTTP客户端协议"""
    
    def send_http(self, payload: HttpPayload) -> HttpResponse:
        """发送HTTP请求"""
        ...

@runtime_checkable
class AssertionUtils(Protocol):
    """断言工具协议"""
    
    def assert_status_and_nodeid(self, response: HttpResponse) -> None:
        """断言状态码和节点ID"""
        ...
    
    def assert_contains(self, content: Any, expected: Any) -> None:
        """断言包含"""
        ...
    
    def assert_not_contains(self, content: Any, expected: str) -> None:
        """断言不包含"""
        ...

# 泛型类型
T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')

class CacheEntry(Generic[T]):
    """缓存条目泛型类"""
    
    def __init__(self, value: T, timestamp: float, ttl: Optional[float] = None):
        self.value = value
        self.timestamp = timestamp
        self.ttl = ttl
    
    def is_expired(self, current_time: float) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return current_time - self.timestamp > self.ttl

# 函数类型
ConfigLoader = Callable[[PathLike], Optional[ConfigDict]]
TestDataLoader = Callable[[PathLike], Optional[TestCaseData]]
ErrorHandler = Callable[[Exception], None]
ResponseValidator = Callable[[HttpResponse], bool]

# 装饰器类型
ApiDecorator = Callable[[Callable[..., HttpResponse]], Callable[..., HttpResponse]]
TestDecorator = Callable[[Callable[..., Any]], Callable[..., Any]]

# 回调函数类型
OnConfigChange = Callable[[str, ConfigDict], None]
OnTestStart = Callable[[str], None]
OnTestEnd = Callable[[str, bool, Optional[Exception]], None]
OnRequestSent = Callable[[HttpPayload], None]
OnResponseReceived = Callable[[HttpResponse, float], None]

# 验证函数类型
ConfigValidator = Callable[[ConfigDict], bool]
TestDataValidator = Callable[[TestCaseData], bool]
ResponseValidator = Callable[[HttpResponse], bool]

# 特殊类型
class BlockIdentifier(NamedTuple):
    """区块标识符"""
    value: Union[str, int]
    type: Literal['number', 'hash', 'tag']

class TransactionHash(str):
    """交易哈希类型"""
    
    def __new__(cls, value: str) -> 'TransactionHash':
        if not isinstance(value, str) or not value.startswith('0x'):
            raise ValueError(f"Invalid transaction hash: {value}")
        return super().__new__(cls, value)

class Address(str):
    """地址类型"""
    
    def __new__(cls, value: str) -> 'Address':
        if not isinstance(value, str) or not value.startswith('0x'):
            raise ValueError(f"Invalid address: {value}")
        return super().__new__(cls, value)

# 链特定类型
class EthereumBlock(TypedDict):
    """以太坊区块结构"""
    number: str
    hash: str
    parentHash: str
    timestamp: str
    gasLimit: str
    gasUsed: str
    transactions: List[Union[str, Dict[str, Any]]]

class SolanaBlock(TypedDict):
    """Solana区块结构"""
    blockhash: str
    previousBlockhash: str
    parentSlot: int
    blockTime: Optional[int]
    transactions: List[Dict[str, Any]]

# 性能监控类型
class PerformanceMetrics(TypedDict):
    """性能指标"""
    request_time: float
    response_time: float
    total_time: float
    request_size: int
    response_size: int
    status_code: int

# 日志相关类型
LogLevel = Literal['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']

class LogEntry(TypedDict):
    """日志条目"""
    timestamp: str
    level: LogLevel
    message: str
    module: str
    function: str
    line: int
    extra: NotRequired[Dict[str, Any]]

# 测试结果类型
class TestResult(TypedDict):
    """测试结果"""
    test_name: str
    status: Literal['passed', 'failed', 'skipped', 'error']
    duration: float
    error_message: NotRequired[str]
    error_type: NotRequired[str]
    traceback: NotRequired[str]

# 批量操作类型
BatchRequest = List[RpcRequest]
BatchResponse = List[RpcResponse]

# 重试配置类型
class RetryConfig(TypedDict):
    """重试配置"""
    max_retries: int
    initial_delay: float
    backoff_multiplier: float
    max_delay: float
    retry_on_status_codes: List[int]
    retry_on_exceptions: List[str]

# 缓存配置类型
class CacheConfig(TypedDict):
    """缓存配置"""
    enabled: bool
    ttl: float
    max_size: int
    cleanup_interval: float

# 导出所有类型
__all__ = [
    # 基础类型
    'JsonValue', 'JsonDict', 'JsonList', 'PathLike',
    
    # HTTP类型
    'HttpMethod', 'HttpHeaders', 'HttpParams', 'HttpResponse', 'HttpPayload',
    
    # 配置类型
    'ConfigDict', 'EnvironmentName', 'UrlConfig', 'ChainConfig',
    
    # 测试类型
    'TestData', 'TestCaseData', 'ChainName', 'MethodName', 'TestCase', 'TestResult',
    
    # RPC类型
    'RpcId', 'RpcParams', 'RpcRequest', 'RpcResponse', 'RpcError',
    'BatchRequest', 'BatchResponse',
    
    # 错误类型
    'ErrorContext', 'ErrorCategory',
    
    # 协议类型
    'Configurable', 'HttpClient', 'AssertionUtils',
    
    # 泛型类型
    'T', 'K', 'V', 'CacheEntry',
    
    # 函数类型
    'ConfigLoader', 'TestDataLoader', 'ErrorHandler', 'ResponseValidator',
    'ApiDecorator', 'TestDecorator',
    
    # 回调类型
    'OnConfigChange', 'OnTestStart', 'OnTestEnd', 
    'OnRequestSent', 'OnResponseReceived',
    
    # 验证类型
    'ConfigValidator', 'TestDataValidator',
    
    # 特殊类型
    'BlockIdentifier', 'TransactionHash', 'Address',
    
    # 链特定类型
    'EthereumBlock', 'SolanaBlock',
    
    # 性能类型
    'PerformanceMetrics',
    
    # 日志类型
    'LogLevel', 'LogEntry',
    
    # 配置类型
    'RetryConfig', 'CacheConfig',
]
