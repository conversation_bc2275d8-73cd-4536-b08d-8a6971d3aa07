"""
装饰器模块

提供API调用日志记录和其他通用装饰器功能。
"""

import functools
from typing import Callable, Any, TypeVar
from loguru import logger

# 泛型类型变量
F = TypeVar('F', bound=Callable[..., Any])


def api_call(func: F) -> F:
    """
    API调用日志记录装饰器

    自动记录API方法的调用开始和结束日志，用于调试和监控。

    Args:
        func: 被装饰的函数

    Returns:
        装饰后的函数，保持原函数的类型签名

    Example:
        >>> @api_call
        ... def eth_chainId(self, url, **data):
        ...     return self._make_rpc_call(url, **data)
        ...
        >>> # 调用时会自动记录日志：
        >>> # INFO: 开始调用接口：eth_chainId
        >>> # INFO: 结束调用接口：eth_chainId
    """

    @functools.wraps(func)
    def inner(*args: Any, **kwargs: Any) -> Any:
        # 输出到控制台的日志级别
        logger.info(f"开始调用接口：{func.__name__}")
        try:
            res = func(*args, **kwargs)
            logger.info(f"结束调用接口：{func.__name__}")
            return res
        except Exception as e:
            logger.error(f"接口调用失败：{func.__name__}, 错误: {e}")
            raise

    return inner  # type: ignore
