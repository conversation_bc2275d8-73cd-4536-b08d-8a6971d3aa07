"""
错误报告器
提供详细的错误定位信息和格式化的错误报告
"""

import json
import traceback
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger
import httpx
from common.error_config_loader import get_error_reporting_config


class ErrorReporter:
    """
    错误报告器
    提供统一的错误报告格式和详细的调试信息
    """
    
    def __init__(self, chain_name: str = None, env_name: str = None):
        """
        初始化错误报告器

        Args:
            chain_name: 链名称，用于获取链特定配置
            env_name: 环境名称，用于获取环境特定配置
        """
        self.test_context = {}
        self.request_history = []
        self.chain_name = chain_name
        self.env_name = env_name

        # 从配置文件加载错误分类
        self.config = get_error_reporting_config(chain_name=chain_name, env_name=env_name)
        self.error_categories = self._load_error_categories_from_config()
    
    def _load_error_categories_from_config(self) -> Dict[str, List[str]]:
        """从配置文件加载错误分类"""
        error_categories = {}
        config_categories = self.config.get('error_categories', {})

        for category, category_config in config_categories.items():
            error_types = category_config.get('error_types', [])
            status_codes = [str(code) for code in category_config.get('status_codes', [])]
            error_categories[category] = error_types + status_codes

        # 如果配置为空，使用默认分类
        if not error_categories:
            error_categories = {
                'network': ['ConnectionError', 'Timeout', 'HTTPError', 'TimeoutError', 'ConnectTimeout', 'ReadTimeout'],
                'assertion': ['AssertionError', 'EnhancedAssertionError'],
                'json': ['JSONDecodeError'],
                'validation': ['ValueError', 'TypeError'],
                'authentication': ['401', '403'],
                'server': ['500', '502', '503', '504'],
                'client': ['400', '404', '422']
            }

        return error_categories

    def set_test_context(self, **context):
        """
        设置测试上下文
        :param context: 测试上下文信息
        """
        self.test_context.update(context)
        self.test_context['timestamp'] = datetime.now().isoformat()
    
    def log_request(self, method: str, url: str, headers: Dict = None,
                   params: Dict = None, json_data: Dict = None, data: Any = None):
        """
        记录请求详情
        :param method: HTTP方法
        :param url: 请求URL
        :param headers: 请求头
        :param params: 查询参数
        :param json_data: JSON数据
        :param data: 表单数据
        """
        # 检查是否启用请求日志
        request_logging_config = self.config.get('request_logging', {})
        if not request_logging_config.get('enabled', True):
            return

        request_info = {
            'timestamp': datetime.now().isoformat(),
            'method': method.upper(),
            'url': url,
            'headers': self._sanitize_headers(headers or {}) if request_logging_config.get('log_headers', True) else {},
            'params': params if request_logging_config.get('log_params', True) else None,
            'json': json_data if request_logging_config.get('log_json_data', True) else None,
            'data': data if request_logging_config.get('log_form_data', True) else None
        }

        self.request_history.append(request_info)

        # 根据配置决定是否显示详细日志
        report_format = self.config.get('report_format', {})
        use_emojis = report_format.get('use_emojis', True)

        # 详细的请求日志
        separator = "=" * report_format.get('max_line_length', 80)
        logger.info(separator)
        logger.info(f"{'📤' if use_emojis else ''} HTTP 请求详情")
        logger.info(separator)
        logger.info(f"{'🔗' if use_emojis else ''} URL: {method.upper()} {url}")
        logger.info(f"{'⏰' if use_emojis else ''} 时间: {request_info['timestamp']}")

        if headers and request_logging_config.get('log_headers', True):
            logger.info(f"{'📋' if use_emojis else ''} 请求头:")
            for key, value in self._sanitize_headers(headers).items():
                logger.info(f"   {key}: {value}")

        if params and request_logging_config.get('log_params', True):
            logger.info(f"{'🔍' if use_emojis else ''} 查询参数: {json.dumps(params, indent=2, ensure_ascii=False)}")

        if json_data and request_logging_config.get('log_json_data', True):
            logger.info(f"{'📦' if use_emojis else ''} JSON 数据:")
            logger.info(json.dumps(json_data, indent=2, ensure_ascii=False))

        if data and request_logging_config.get('log_form_data', True):
            logger.info(f"{'📄' if use_emojis else ''} 表单数据: {data}")
    
    def log_response(self, response: httpx.Response, duration: float = None):
        """
        记录响应详情
        :param response: HTTP响应对象
        :param duration: 请求耗时
        """
        if not self.request_history:
            logger.warning("没有找到对应的请求记录")
            return
        
        # 更新最后一个请求的响应信息
        last_request = self.request_history[-1]
        
        try:
            response_text = response.text
            try:
                response_json = response.json()
            except:
                response_json = None
        except:
            response_text = "<无法读取响应内容>"
            response_json = None
        
        response_info = {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'text': response_text,
            'json': response_json,
            'duration': duration,
            'size': len(response_text) if response_text else 0
        }
        
        last_request['response'] = response_info
        
        # 详细的响应日志
        logger.info("=" * 80)
        logger.info("📥 HTTP 响应详情")
        logger.info("=" * 80)
        logger.info(f"📊 状态码: {response.status_code} {self._get_status_description(response.status_code)}")
        
        if duration:
            logger.info(f"⏱️  耗时: {duration:.3f}秒")
        
        logger.info(f"📏 大小: {len(response_text) if response_text else 0} 字节")
        
        logger.info("📋 响应头:")
        for key, value in response.headers.items():
            logger.info(f"   {key}: {value}")
        
        if response_json:
            logger.info("📦 JSON 响应:")
            logger.info(json.dumps(response_json, indent=2, ensure_ascii=False))
        elif response_text:
            # 限制响应内容长度
            display_text = response_text[:1000] + "..." if len(response_text) > 1000 else response_text
            logger.info(f"📄 响应内容: {display_text}")
    
    def categorize_error(self, error: Exception, response: httpx.Response = None) -> str:
        """
        错误分类
        :param error: 异常对象
        :param response: HTTP响应对象
        :return: 错误类别
        """
        error_type = type(error).__name__
        
        # 检查HTTP状态码
        if response and hasattr(response, 'status_code'):
            status_code = str(response.status_code)
            for category, indicators in self.error_categories.items():
                if status_code in indicators:
                    return category
        
        # 检查异常类型
        for category, error_types in self.error_categories.items():
            if error_type in error_types:
                return category
        
        return 'unknown'
    
    def generate_error_suggestions(self, error_category: str, error: Exception, 
                                 response: httpx.Response = None) -> List[str]:
        """
        生成错误建议
        :param error_category: 错误类别
        :param error: 异常对象
        :param response: HTTP响应对象
        :return: 建议列表
        """
        suggestions = []
        
        if error_category == 'network':
            suggestions.extend([
                "检查网络连接是否正常",
                "验证目标服务器是否可达",
                "检查防火墙和代理设置",
                "尝试增加请求超时时间"
            ])
        
        elif error_category == 'authentication':
            suggestions.extend([
                "检查API密钥是否正确",
                "验证认证头是否正确设置",
                "确认账户权限是否足够",
                "检查token是否已过期"
            ])
        
        elif error_category == 'server':
            suggestions.extend([
                "服务器内部错误，请稍后重试",
                "检查服务器状态和负载",
                "联系API提供方确认服务状态",
                "查看服务器日志获取更多信息"
            ])
        
        elif error_category == 'client':
            suggestions.extend([
                "检查请求参数是否正确",
                "验证请求格式是否符合API规范",
                "确认请求的资源是否存在",
                "检查请求数据的类型和格式"
            ])
        
        elif error_category == 'assertion':
            suggestions.extend([
                "检查API响应格式是否符合预期",
                "验证测试数据是否正确",
                "确认API行为是否发生变化",
                "检查测试环境配置"
            ])
        
        elif error_category == 'json':
            suggestions.extend([
                "检查响应内容是否为有效JSON",
                "验证API是否返回了错误页面",
                "确认Content-Type是否正确",
                "检查响应是否被截断"
            ])
        
        else:
            suggestions.extend([
                "检查错误详情和堆栈跟踪",
                "验证测试环境和配置",
                "查看相关文档和示例",
                "联系开发团队获取支持"
            ])
        
        return suggestions
    
    def format_error_report(self, error: Exception, response: httpx.Response = None) -> str:
        """
        格式化错误报告
        :param error: 异常对象
        :param response: HTTP响应对象
        :return: 格式化的错误报告
        """
        error_category = self.categorize_error(error, response)
        suggestions = self.generate_error_suggestions(error_category, error, response)
        
        report_lines = [
            "🚨 测试失败详细报告",
            "=" * 80,
            f"❌ 错误类型: {type(error).__name__}",
            f"🏷️  错误类别: {error_category}",
            f"💬 错误信息: {str(error)}",
            f"⏰ 发生时间: {datetime.now().isoformat()}",
            ""
        ]
        
        # 测试上下文信息
        if self.test_context:
            report_lines.extend([
                "📋 测试上下文:",
                "-" * 40
            ])
            for key, value in self.test_context.items():
                report_lines.append(f"   {key}: {value}")
            report_lines.append("")
        
        # 最近的请求信息
        if self.request_history:
            last_request = self.request_history[-1]
            report_lines.extend([
                "📤 最近的请求:",
                "-" * 40,
                f"   方法: {last_request['method']}",
                f"   URL: {last_request['url']}",
                f"   时间: {last_request['timestamp']}"
            ])
            
            if last_request.get('json'):
                report_lines.append(f"   JSON: {json.dumps(last_request['json'], ensure_ascii=False)}")
            
            if 'response' in last_request:
                resp = last_request['response']
                report_lines.extend([
                    f"   响应状态: {resp['status_code']}",
                    f"   响应大小: {resp['size']} 字节"
                ])
                if resp.get('duration'):
                    report_lines.append(f"   耗时: {resp['duration']:.3f}秒")
            
            report_lines.append("")
        
        # 错误建议
        if suggestions:
            report_lines.extend([
                "💡 解决建议:",
                "-" * 40
            ])
            for i, suggestion in enumerate(suggestions, 1):
                report_lines.append(f"   {i}. {suggestion}")
            report_lines.append("")
        
        # 堆栈跟踪
        report_lines.extend([
            "🔍 堆栈跟踪:",
            "-" * 40,
            traceback.format_exc(),
            "=" * 80
        ])
        
        return "\n".join(report_lines)
    
    def _sanitize_headers(self, headers: Dict) -> Dict:
        """
        清理敏感的请求头信息
        :param headers: 原始请求头
        :return: 清理后的请求头
        """
        request_logging_config = self.config.get('request_logging', {})

        # 如果不需要清理敏感数据，直接返回原始头部
        if not request_logging_config.get('sanitize_sensitive_data', True):
            return headers

        # 从配置获取敏感头部列表
        sensitive_keys = request_logging_config.get('sensitive_headers',
                                                   ['authorization', 'x-api-key', 'cookie', 'token', 'api-key'])
        sanitized = {}

        for key, value in headers.items():
            if key.lower() in [k.lower() for k in sensitive_keys]:
                sanitized[key] = "***HIDDEN***"
            else:
                sanitized[key] = value

        return sanitized
    
    def _get_status_description(self, status_code: int) -> str:
        """
        获取HTTP状态码描述
        :param status_code: 状态码
        :return: 状态描述
        """
        status_descriptions = {
            200: "OK",
            201: "Created",
            400: "Bad Request",
            401: "Unauthorized",
            403: "Forbidden",
            404: "Not Found",
            422: "Unprocessable Entity",
            500: "Internal Server Error",
            502: "Bad Gateway",
            503: "Service Unavailable",
            504: "Gateway Timeout"
        }
        
        return status_descriptions.get(status_code, "Unknown")
    
    def clear_history(self):
        """清空请求历史"""
        self.request_history.clear()
        self.test_context.clear()


# 创建默认的错误报告器实例
default_error_reporter = ErrorReporter()


def get_error_reporter(chain_name: str = None, env_name: str = None) -> ErrorReporter:
    """
    获取错误报告器实例

    Args:
        chain_name: 链名称
        env_name: 环境名称

    Returns:
        ErrorReporter: 错误报告器实例
    """
    if chain_name or env_name:
        return ErrorReporter(chain_name=chain_name, env_name=env_name)
    return default_error_reporter


# 提供便捷的函数接口
def set_test_context(**context):
    """设置测试上下文的便捷函数"""
    return default_error_reporter.set_test_context(**context)


def log_request(method: str, url: str, **kwargs):
    """记录请求的便捷函数"""
    return default_error_reporter.log_request(method, url, **kwargs)


def log_response(response: httpx.Response, duration: float = None):
    """记录响应的便捷函数"""
    return default_error_reporter.log_response(response, duration)


def format_error_report(error: Exception, response: httpx.Response = None) -> str:
    """格式化错误报告的便捷函数"""
    return default_error_reporter.format_error_report(error, response)
