"""
增强的配置管理器
从 utils.py 中提取的配置管理功能，支持pytest-xdist并发执行
"""

import os
import time
import threading
import fcntl
import tempfile
import pickle
from typing import Dict, Any, Optional
import yaml
from loguru import logger


class EnhancedConfigManager:
    """
    增强的配置管理器
    支持文件锁、进程间共享缓存、热更新等功能
    """
    
    def __init__(self):
        """初始化配置管理器"""
        # 配置文件缓存机制（支持pytest-xdist并发）
        self._file_cache: Dict[str, Any] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._cache_lock = threading.RLock()
        self._file_locks: Dict[str, Any] = {}
        self._temp_dir = tempfile.gettempdir()
    
    def _get_file_lock(self, file_path: str):
        """
        获取文件锁，支持进程间同步
        :param file_path: 文件路径
        :return: 文件锁对象
        """
        if file_path not in self._file_locks:
            # 创建锁文件路径
            lock_file_name = f"config_lock_{abs(hash(file_path))}.lock"
            lock_file_path = os.path.join(self._temp_dir, lock_file_name)
            
            try:
                # 创建锁文件
                lock_file = open(lock_file_path, 'w')
                self._file_locks[file_path] = lock_file
            except Exception as e:
                logger.warning(f"创建文件锁失败 {file_path}: {e}")
                self._file_locks[file_path] = None
        
        return self._file_locks[file_path]

    def _acquire_file_lock(self, file_path: str, timeout: int = 5) -> bool:
        """
        获取文件锁
        :param file_path: 文件路径
        :param timeout: 超时时间（秒）
        :return: 是否成功获取锁
        """
        lock_file = self._get_file_lock(file_path)
        if lock_file is None:
            return True  # 如果无法创建锁文件，跳过锁机制
        
        try:
            # 尝试获取排他锁，支持超时
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    return True
                except BlockingIOError:
                    time.sleep(0.01)  # 等待10毫秒后重试
            
            logger.warning(f"获取文件锁超时: {file_path}")
            return False
        except Exception as e:
            logger.warning(f"获取文件锁失败 {file_path}: {e}")
            return True  # 出错时跳过锁机制

    def _release_file_lock(self, file_path: str):
        """
        释放文件锁
        :param file_path: 文件路径
        """
        lock_file = self._get_file_lock(file_path)
        if lock_file is None:
            return
        
        try:
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
        except Exception as e:
            logger.warning(f"释放文件锁失败 {file_path}: {e}")

    def _get_shared_cache_path(self, file_name: str) -> str:
        """
        获取共享缓存文件路径
        :param file_name: 原始文件路径
        :return: 共享缓存文件路径
        """
        # 使用文件路径的哈希值作为缓存文件名
        cache_name = f"config_cache_{abs(hash(file_name))}.pkl"
        return os.path.join(self._temp_dir, cache_name)

    def _load_from_shared_cache(self, file_name: str, expected_mtime: float) -> Optional[Any]:
        """
        从进程间共享缓存加载数据
        :param file_name: 文件路径
        :param expected_mtime: 期望的文件修改时间
        :return: 缓存的数据或None
        """
        try:
            cache_path = self._get_shared_cache_path(file_name)
            if not os.path.exists(cache_path):
                return None
            
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
            
            # 检查缓存是否有效
            if (cache_data.get('mtime') == expected_mtime and 
                cache_data.get('file_path') == file_name):
                return cache_data.get('data')
            
            return None
        except Exception as e:
            logger.trace(f"加载共享缓存失败 {file_name}: {e}")
            return None

    def _save_to_shared_cache(self, file_name: str, data: Any, mtime: float):
        """
        保存数据到进程间共享缓存
        :param file_name: 文件路径
        :param data: 要缓存的数据
        :param mtime: 文件修改时间
        """
        try:
            cache_path = self._get_shared_cache_path(file_name)
            cache_data = {
                'file_path': file_name,
                'data': data,
                'mtime': mtime,
                'cached_at': time.time()
            }
            
            with open(cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
            
            logger.trace(f"已保存到共享缓存: {os.path.basename(file_name)}")
        except Exception as e:
            logger.trace(f"保存共享缓存失败 {file_name}: {e}")

    def load_yaml(self, file_name: str) -> Any:
        """
        读取yaml文件 - 支持pytest-xdist并发执行的缓存机制
        :param file_name: 文件路径
        :return: YAML数据
        """
        with self._cache_lock:
            try:
                # 检查文件是否存在
                if not os.path.exists(file_name):
                    raise FileNotFoundError(f"配置文件不存在: {file_name}")
                
                # 获取文件修改时间
                current_mtime = os.path.getmtime(file_name)
                cached_mtime = self._file_timestamps.get(file_name, 0)
                
                # 检查是否需要重新加载
                if (file_name not in self._file_cache or 
                    current_mtime > cached_mtime):
                    
                    # 尝试从进程间共享缓存加载
                    shared_data = self._load_from_shared_cache(file_name, current_mtime)
                    if shared_data is not None:
                        self._file_cache[file_name] = shared_data
                        self._file_timestamps[file_name] = current_mtime
                        logger.trace(f"从共享缓存加载: {os.path.basename(file_name)}")
                        return shared_data
                    
                    # 获取文件锁，防止多进程同时读取
                    if self._acquire_file_lock(file_name):
                        try:
                            # 再次检查是否已被其他进程加载
                            shared_data = self._load_from_shared_cache(file_name, current_mtime)
                            if shared_data is not None:
                                self._file_cache[file_name] = shared_data
                                self._file_timestamps[file_name] = current_mtime
                                logger.trace(f"其他进程已加载: {os.path.basename(file_name)}")
                                return shared_data
                            
                            # 重新加载文件
                            with open(file_name, 'r', encoding='utf-8') as f:
                                yaml_data = yaml.safe_load(f)
                            
                            # 更新本地缓存
                            self._file_cache[file_name] = yaml_data
                            self._file_timestamps[file_name] = current_mtime
                            
                            # 保存到共享缓存
                            self._save_to_shared_cache(file_name, yaml_data, current_mtime)
                            
                            logger.debug(f"配置文件已重新加载: {os.path.basename(file_name)}")
                            
                        finally:
                            self._release_file_lock(file_name)
                    else:
                        # 获取锁失败，使用现有缓存或重新读取
                        if file_name in self._file_cache:
                            logger.warning(f"获取锁失败，使用现有缓存: {os.path.basename(file_name)}")
                        else:
                            # 无缓存且获取锁失败，直接读取文件
                            with open(file_name, 'r', encoding='utf-8') as f:
                                yaml_data = yaml.safe_load(f)
                            self._file_cache[file_name] = yaml_data
                            self._file_timestamps[file_name] = current_mtime
                            logger.warning(f"无锁直接读取: {os.path.basename(file_name)}")
                else:
                    logger.trace(f"使用缓存的配置文件: {os.path.basename(file_name)}")
                
                return self._file_cache[file_name]
                
            except Exception as e:
                logger.error(f'yaml文件读取失败，文件名称：{file_name}, 错误: {e}')
                # 如果加载失败，返回缓存的版本（如果有的话）
                return self._file_cache.get(file_name, {})

    def reload_config(self, file_name: Optional[str] = None):
        """
        手动重新加载配置文件（支持进程间同步）
        :param file_name: 指定文件路径，None表示重新加载所有缓存的配置
        """
        with self._cache_lock:
            if file_name:
                # 重新加载指定文件
                if file_name in self._file_cache:
                    del self._file_cache[file_name]
                    del self._file_timestamps[file_name]
                
                # 清理共享缓存
                self._clear_shared_cache(file_name)
                
                # 重新加载
                self.load_yaml(file_name)
                logger.info(f"已重新加载配置文件: {os.path.basename(file_name)}")
            else:
                # 重新加载所有缓存的配置
                cached_files = list(self._file_cache.keys())
                self._file_cache.clear()
                self._file_timestamps.clear()
                
                # 清理所有共享缓存
                for cached_file in cached_files:
                    self._clear_shared_cache(cached_file)
                
                # 重新加载所有文件
                for cached_file in cached_files:
                    self.load_yaml(cached_file)
                logger.info("已重新加载所有缓存的配置文件")

    def clear_cache(self):
        """清空配置缓存（包括共享缓存）"""
        with self._cache_lock:
            # 清理共享缓存
            cached_files = list(self._file_cache.keys())
            for cached_file in cached_files:
                self._clear_shared_cache(cached_file)
            
            # 清理本地缓存
            self._file_cache.clear()
            self._file_timestamps.clear()
            logger.info("配置缓存已清空")

    def _clear_shared_cache(self, file_name: str):
        """
        清理指定文件的共享缓存
        :param file_name: 文件路径
        """
        try:
            cache_path = self._get_shared_cache_path(file_name)
            if os.path.exists(cache_path):
                os.remove(cache_path)
                logger.trace(f"已清理共享缓存: {os.path.basename(file_name)}")
        except Exception as e:
            logger.trace(f"清理共享缓存失败 {file_name}: {e}")

    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息（包括共享缓存）
        :return: 缓存信息字典
        """
        with self._cache_lock:
            # 检查共享缓存文件
            shared_caches = []
            for file_path in self._file_cache.keys():
                cache_path = self._get_shared_cache_path(file_path)
                if os.path.exists(cache_path):
                    try:
                        cache_stat = os.stat(cache_path)
                        shared_caches.append({
                            'file': os.path.basename(file_path),
                            'cache_size': cache_stat.st_size,
                            'cache_mtime': cache_stat.st_mtime
                        })
                    except Exception:
                        pass
            
            return {
                'local_cached_files': [os.path.basename(f) for f in self._file_cache.keys()],
                'local_cache_size': len(self._file_cache),
                'file_timestamps': {os.path.basename(k): v for k, v in self._file_timestamps.items()},
                'shared_caches': shared_caches,
                'process_id': os.getpid(),
                'temp_dir': self._temp_dir
            }


# 创建默认的增强配置管理器实例
default_enhanced_config_manager = EnhancedConfigManager()


# 提供便捷的函数接口
def load_yaml(file_name: str) -> Any:
    """加载YAML文件的便捷函数"""
    return default_enhanced_config_manager.load_yaml(file_name)


def reload_config(file_name: Optional[str] = None):
    """重新加载配置的便捷函数"""
    return default_enhanced_config_manager.reload_config(file_name)


def clear_config_cache():
    """清空配置缓存的便捷函数"""
    return default_enhanced_config_manager.clear_cache()


def get_config_cache_info() -> Dict[str, Any]:
    """获取缓存信息的便捷函数"""
    return default_enhanced_config_manager.get_cache_info()
