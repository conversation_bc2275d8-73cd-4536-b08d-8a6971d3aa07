"""
模板处理模块
专门处理模板变量替换和字符串模板处理
"""

import yaml
from string import Template
from loguru import logger
from typing import Any, Dict, Union


class TemplateUtils:
    """
    模板处理工具类
    提供模板变量替换功能
    """

    @staticmethod
    def handle_template(source_data: Any, replace_data: Dict[str, str]) -> Any:
        """
        替换文本变量
        :param source_data: 源数据
        :param replace_data: 需要替换的内容
        :return: 替换后的数据
        """
        try:
            # 将源数据转换为字符串
            source_str = str(source_data)
            
            # 使用Template进行安全替换
            template = Template(source_str)
            result_str = template.safe_substitute(**replace_data)
            
            # 尝试解析为YAML/JSON格式
            try:
                result = yaml.safe_load(result_str)
                logger.debug(f"模板替换成功，替换变量: {list(replace_data.keys())}")
                return result
            except yaml.YAMLError:
                # 如果解析失败，返回字符串
                logger.debug(f"模板替换成功（字符串格式），替换变量: {list(replace_data.keys())}")
                return result_str
                
        except Exception as e:
            logger.error(f"模板处理失败: {e}")
            logger.error(f"源数据类型: {type(source_data).__name__}, 替换数据: {replace_data}")
            raise e

    @staticmethod
    def replace_variables_in_dict(data: Dict[str, Any], variables: Dict[str, str]) -> Dict[str, Any]:
        """
        递归替换字典中的变量
        :param data: 源字典数据
        :param variables: 变量字典
        :return: 替换后的字典
        """
        if not isinstance(data, dict):
            return data
        
        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                # 字符串值进行模板替换
                template = Template(value)
                result[key] = template.safe_substitute(**variables)
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                result[key] = TemplateUtils.replace_variables_in_dict(value, variables)
            elif isinstance(value, list):
                # 处理列表
                result[key] = TemplateUtils.replace_variables_in_list(value, variables)
            else:
                # 其他类型保持不变
                result[key] = value
        
        return result

    @staticmethod
    def replace_variables_in_list(data: list, variables: Dict[str, str]) -> list:
        """
        递归替换列表中的变量
        :param data: 源列表数据
        :param variables: 变量字典
        :return: 替换后的列表
        """
        if not isinstance(data, list):
            return data
        
        result = []
        for item in data:
            if isinstance(item, str):
                # 字符串项进行模板替换
                template = Template(item)
                result.append(template.safe_substitute(**variables))
            elif isinstance(item, dict):
                # 递归处理字典项
                result.append(TemplateUtils.replace_variables_in_dict(item, variables))
            elif isinstance(item, list):
                # 递归处理嵌套列表
                result.append(TemplateUtils.replace_variables_in_list(item, variables))
            else:
                # 其他类型保持不变
                result.append(item)
        
        return result

    @staticmethod
    def extract_variables(template_str: str) -> list:
        """
        从模板字符串中提取变量名
        :param template_str: 模板字符串
        :return: 变量名列表
        """
        try:
            template = Template(template_str)
            # 使用Template的内部方法获取变量
            import re
            pattern = r'\$\{([^}]+)\}|\$([a-zA-Z_][a-zA-Z0-9_]*)'
            matches = re.findall(pattern, template_str)
            variables = []
            for match in matches:
                # match是元组，取非空的部分
                var_name = match[0] if match[0] else match[1]
                if var_name and var_name not in variables:
                    variables.append(var_name)
            return variables
        except Exception as e:
            logger.warning(f"提取模板变量失败: {e}")
            return []

    @staticmethod
    def validate_template(template_str: str, variables: Dict[str, str]) -> bool:
        """
        验证模板是否可以被正确替换
        :param template_str: 模板字符串
        :param variables: 变量字典
        :return: 是否有效
        """
        try:
            template = Template(template_str)
            # 尝试进行替换
            template.substitute(**variables)
            return True
        except KeyError as e:
            logger.warning(f"模板验证失败，缺少变量: {e}")
            return False
        except Exception as e:
            logger.warning(f"模板验证失败: {e}")
            return False

    @staticmethod
    def safe_substitute_with_defaults(template_str: str, variables: Dict[str, str], 
                                    defaults: Dict[str, str] = None) -> str:
        """
        安全替换模板变量，支持默认值
        :param template_str: 模板字符串
        :param variables: 变量字典
        :param defaults: 默认值字典
        :return: 替换后的字符串
        """
        try:
            template = Template(template_str)
            
            # 合并变量和默认值
            all_variables = {}
            if defaults:
                all_variables.update(defaults)
            all_variables.update(variables)
            
            # 进行安全替换
            result = template.safe_substitute(**all_variables)
            return result
            
        except Exception as e:
            logger.error(f"模板安全替换失败: {e}")
            return template_str


# 创建默认的模板工具实例
default_template_utils = TemplateUtils()


# 提供便捷的函数接口，保持向后兼容
def handle_template(source_data: Any, replace_data: Dict[str, str]) -> Any:
    """处理模板的便捷函数"""
    return default_template_utils.handle_template(source_data, replace_data)


def replace_variables_in_dict(data: Dict[str, Any], variables: Dict[str, str]) -> Dict[str, Any]:
    """替换字典中变量的便捷函数"""
    return default_template_utils.replace_variables_in_dict(data, variables)


def replace_variables_in_list(data: list, variables: Dict[str, str]) -> list:
    """替换列表中变量的便捷函数"""
    return default_template_utils.replace_variables_in_list(data, variables)
