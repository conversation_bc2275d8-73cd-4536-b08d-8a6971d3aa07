"""IP模式处理模块，负责检测IP模式并处理相关的SSL和Host Header配置"""
import re
import sys
from urllib.parse import urlparse
from loguru import logger


class IPModeHandler:
    """处理IP模式相关的逻辑"""
    
    def __init__(self, url_paths_mapping=None):
        """
        初始化IP模式处理器
        :param url_paths_mapping: URL路径到链名的映射字典
        """
        self.url_paths_mapping = url_paths_mapping or {}
    
    def is_ip_mode(self):
        """
        检查当前是否为IP模式
        :return: (is_ip_mode, original_env_value)
        """
        try:
            if '--env' in sys.argv:
                env_index = sys.argv.index('--env')
                if env_index + 1 < len(sys.argv):
                    original_env_value = sys.argv[env_index + 1]
                    # 使用正则表达式严格匹配 IP 地址格式
                    if re.fullmatch(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', original_env_value):
                        return True, original_env_value
            return False, None
        except Exception as e:
            logger.error(f"检查 sys.argv 获取 --env 参数时出错: {e}")
            return False, None
    
    def find_chain_name_by_url(self, request_url):
        """
        根据请求URL查找对应的链名
        :param request_url: 请求URL
        :return: 匹配的链名，如果未找到返回None
        """
        if not request_url or not self.url_paths_mapping:
            return None
        
        try:
            parsed_request_url = urlparse(request_url)
            request_path = parsed_request_url.path.rstrip('/')
            
            # 1. 尝试精确匹配路径
            if request_path in self.url_paths_mapping:
                chain_name = self.url_paths_mapping[request_path]
                logger.trace(f"IP 模式: URL '{request_url}' 通过精确路径匹配到链 '{chain_name}'")
                return chain_name
            
            # 2. 如果精确匹配失败，尝试查找最长的前缀匹配
            best_match_len = 0
            matched_chain = None
            for config_path, c_name in self.url_paths_mapping.items():
                if config_path and request_path.startswith(config_path) and len(config_path) > best_match_len:
                    best_match_len = len(config_path)
                    matched_chain = c_name
            
            if matched_chain:
                logger.trace(f"IP 模式: URL '{request_url}' 通过前缀匹配到链 '{matched_chain}'")
                return matched_chain
            
            return None
        except Exception as e:
            logger.warning(f"解析请求 URL '{request_url}' 或查找链名时出错: {e}")
            return None
    
    def apply_ip_mode_config(self, request_headers, request_url, original_env_value):
        """
        应用IP模式配置：禁用SSL验证并添加Host Header
        :param request_headers: 请求头字典（会被修改）
        :param request_url: 请求URL
        :param original_env_value: 原始环境值（IP地址）
        :return: (verify_ssl, host_header_added)
        """
        verify_ssl = False
        host_header_added = False
        
        logger.warning(f"IP 模式 (--env={original_env_value}): 禁用 SSL 证书验证 for URL: {request_url}")
        
        # 查找链名并添加Host Header
        chain_name = self.find_chain_name_by_url(request_url)
        if chain_name:
            host_header_value = f"{chain_name}.blockpi.network"
            request_headers['Host'] = host_header_value
            logger.debug(f"IP 模式: 添加 Host Header: {host_header_value} for URL: {request_url}")
            host_header_added = True
        else:
            logger.warning(f"IP 模式: 无法为 URL '{request_url}' 找到对应的链名映射，未添加 Host header。检查 conf/config.yaml 配置。")
        
        return verify_ssl, host_header_added
    
    def update_url_paths_mapping(self, url_paths_mapping):
        """
        更新URL路径映射
        :param url_paths_mapping: 新的URL路径到链名映射
        """
        self.url_paths_mapping = url_paths_mapping or {}
        logger.debug(f"更新IP模式URL路径映射，共 {len(self.url_paths_mapping)} 个映射")