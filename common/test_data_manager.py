"""
测试数据管理器
实现模板继承机制，减少测试数据重复
"""

import yaml
import copy
from typing import Dict, Any, List, Optional
from loguru import logger


class DataManager:
    """
    测试数据管理器
    支持模板继承、数据合并、变量替换等功能
    """
    
    def __init__(self):
        """初始化测试数据管理器"""
        self._templates = {}
        self._chains = {}
        self._loaded = False
    
    def load_templates(self, templates_file: str):
        """
        加载模板文件
        :param templates_file: 模板文件路径
        """
        try:
            with open(templates_file, 'r', encoding='utf-8') as f:
                self._templates = yaml.safe_load(f) or {}
            logger.info(f"模板文件加载成功: {templates_file}")
        except Exception as e:
            logger.error(f"模板文件加载失败: {templates_file}, 错误: {e}")
            self._templates = {}
    
    def load_chains(self, chains_file: str):
        """
        加载链配置文件
        :param chains_file: 链配置文件路径
        """
        try:
            with open(chains_file, 'r', encoding='utf-8') as f:
                self._chains = yaml.safe_load(f) or {}
            logger.info(f"链配置文件加载成功: {chains_file}")
        except Exception as e:
            logger.error(f"链配置文件加载失败: {chains_file}, 错误: {e}")
            self._chains = {}
    
    def get_chain_data(self, chain_name: str) -> Dict[str, Any]:
        """
        获取指定链的测试数据（应用模板继承）
        :param chain_name: 链名称
        :return: 链的测试数据
        """
        if chain_name not in self._chains:
            logger.warning(f"链 '{chain_name}' 不存在")
            return {}
        
        chain_config = self._chains[chain_name]
        
        # 如果没有继承模板，直接返回
        if 'extends' not in chain_config:
            return chain_config
        
        # 应用模板继承
        return self._apply_inheritance(chain_config)
    
    def _apply_inheritance(self, chain_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用模板继承
        :param chain_config: 链配置
        :return: 应用继承后的配置
        """
        extends = chain_config.get('extends')
        if not extends:
            return chain_config
        
        # 获取基础模板
        if isinstance(extends, str):
            base_template = self._get_template(extends)
        elif isinstance(extends, list):
            # 多重继承，按顺序合并
            base_template = {}
            for template_name in extends:
                template = self._get_template(template_name)
                base_template = self._deep_merge(base_template, template)
        else:
            logger.warning(f"无效的继承配置: {extends}")
            return chain_config
        
        # 合并基础模板和链特定配置
        result = self._deep_merge(base_template, chain_config)
        
        # 移除继承标记
        if 'extends' in result:
            del result['extends']
        
        return result
    
    def _get_template(self, template_name: str) -> Dict[str, Any]:
        """
        获取模板数据
        :param template_name: 模板名称
        :return: 模板数据
        """
        if template_name not in self._templates:
            logger.warning(f"模板 '{template_name}' 不存在")
            return {}
        
        template = self._templates[template_name]
        
        # 如果模板也有继承，递归处理
        if 'extends' in template:
            return self._apply_inheritance(template)
        
        return copy.deepcopy(template)
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并两个字典
        :param base: 基础字典
        :param override: 覆盖字典
        :return: 合并后的字典
        """
        result = copy.deepcopy(base)
        
        for key, value in override.items():
            if key in result:
                if isinstance(result[key], dict) and isinstance(value, dict):
                    # 递归合并字典
                    result[key] = self._deep_merge(result[key], value)
                elif isinstance(result[key], list) and isinstance(value, list):
                    # 合并列表（覆盖模式）
                    result[key] = value
                else:
                    # 直接覆盖
                    result[key] = value
            else:
                result[key] = copy.deepcopy(value)
        
        return result
    
    def get_all_chains(self) -> List[str]:
        """
        获取所有链名称
        :return: 链名称列表
        """
        return list(self._chains.keys())
    
    def get_template_names(self) -> List[str]:
        """
        获取所有模板名称
        :return: 模板名称列表
        """
        return list(self._templates.keys())
    
    def validate_inheritance(self) -> Dict[str, List[str]]:
        """
        验证继承关系
        :return: 验证结果，包含错误信息
        """
        errors = {}
        
        for chain_name, chain_config in self._chains.items():
            chain_errors = []
            
            if 'extends' in chain_config:
                extends = chain_config['extends']
                
                if isinstance(extends, str):
                    if extends not in self._templates:
                        chain_errors.append(f"模板 '{extends}' 不存在")
                elif isinstance(extends, list):
                    for template_name in extends:
                        if template_name not in self._templates:
                            chain_errors.append(f"模板 '{template_name}' 不存在")
                else:
                    chain_errors.append(f"无效的继承配置: {extends}")
            
            if chain_errors:
                errors[chain_name] = chain_errors
        
        return errors
    
    def generate_optimized_data(self) -> Dict[str, Any]:
        """
        生成优化后的测试数据（应用所有继承）
        :return: 优化后的测试数据
        """
        result = {}
        
        for chain_name in self._chains:
            result[chain_name] = self.get_chain_data(chain_name)
        
        return result
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据统计信息
        :return: 统计信息
        """
        total_chains = len(self._chains)
        chains_with_inheritance = sum(1 for config in self._chains.values() if 'extends' in config)
        total_templates = len(self._templates)
        
        # 计算数据重复度
        all_methods = set()
        for chain_config in self._chains.values():
            for method in chain_config.keys():
                if method != 'extends':
                    all_methods.add(method)
        
        return {
            'total_chains': total_chains,
            'chains_with_inheritance': chains_with_inheritance,
            'inheritance_ratio': chains_with_inheritance / total_chains if total_chains > 0 else 0,
            'total_templates': total_templates,
            'unique_methods': len(all_methods),
            'templates': list(self._templates.keys()),
            'chains': list(self._chains.keys())
        }


# 创建默认的测试数据管理器实例
default_test_data_manager = DataManager()


# 提供便捷的函数接口
def load_test_templates(templates_file: str):
    """加载测试模板的便捷函数"""
    return default_test_data_manager.load_templates(templates_file)


def load_test_chains(chains_file: str):
    """加载测试链配置的便捷函数"""
    return default_test_data_manager.load_chains(chains_file)


def get_chain_test_data(chain_name: str) -> Dict[str, Any]:
    """获取链测试数据的便捷函数"""
    return default_test_data_manager.get_chain_data(chain_name)


def validate_test_inheritance() -> Dict[str, List[str]]:
    """验证测试继承关系的便捷函数"""
    return default_test_data_manager.validate_inheritance()


def get_test_data_statistics() -> Dict[str, Any]:
    """获取测试数据统计的便捷函数"""
    return default_test_data_manager.get_statistics()
