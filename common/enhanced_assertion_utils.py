"""
增强的断言工具模块
提供详细的错误信息和上下文
"""

import json
from typing import Any, Dict, Optional
from loguru import logger
import httpx
from common.error_reporter import default_error_reporter


class EnhancedAssertionError(AssertionError):
    """增强的断言错误，包含更多上下文信息"""
    
    def __init__(self, message: str, context: Dict[str, Any] = None):
        super().__init__(message)
        self.context = context or {}


class EnhancedAssertionUtils:
    """
    增强的断言工具类
    提供详细的错误信息和调试上下文
    """

    @staticmethod
    def assert_status_and_nodeid(response: httpx.Response, expected_status: int = 200):
        """
        断言响应码和节点id（增强版）
        :param response: 响应对象
        :param expected_status: 期望的状态码
        :raises EnhancedAssertionError: 当断言失败时抛出增强的异常
        """
        try:
            # 设置断言上下文
            default_error_reporter.set_test_context(
                assertion_type="status_and_nodeid",
                expected_status=expected_status,
                actual_status=response.status_code if response else None,
                response_size=len(response.text) if response and hasattr(response, 'text') else 0
            )
            
            # 断言响应对象存在
            if response is None:
                raise EnhancedAssertionError(
                    "响应对象为空，可能是网络请求失败",
                    {"expected": "有效的响应对象", "actual": None}
                )
            
            # 断言响应状态码
            if response.status_code != expected_status:
                context = {
                    "expected_status": expected_status,
                    "actual_status": response.status_code,
                    "response_headers": dict(response.headers),
                    "response_text": response.text[:500] + "..." if len(response.text) > 500 else response.text
                }
                
                error_msg = (
                    f"HTTP状态码断言失败\n"
                    f"期望状态码: {expected_status}\n"
                    f"实际状态码: {response.status_code}\n"
                    f"响应内容: {response.text[:200]}..."
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            # 解析响应内容
            try:
                response_json = response.json()
            except json.JSONDecodeError as e:
                context = {
                    "error": "JSON解析失败",
                    "response_text": response.text[:500],
                    "content_type": response.headers.get('content-type', 'unknown')
                }
                
                error_msg = (
                    f"响应JSON解析失败\n"
                    f"错误: {str(e)}\n"
                    f"响应内容: {response.text[:200]}...\n"
                    f"Content-Type: {response.headers.get('content-type', 'unknown')}"
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            # 断言节点ID存在
            if 'id' not in response_json:
                context = {
                    "error": "缺少id字段",
                    "response_json": response_json,
                    "available_fields": list(response_json.keys()) if isinstance(response_json, dict) else "非字典类型"
                }
                
                error_msg = (
                    f"响应中缺少 'id' 字段\n"
                    f"可用字段: {list(response_json.keys()) if isinstance(response_json, dict) else '响应不是字典类型'}\n"
                    f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)[:300]}..."
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            logger.debug(f"✅ 状态码和节点ID断言通过: status={response.status_code}, id={response_json.get('id')}")
            
        except EnhancedAssertionError:
            raise
        except Exception as e:
            # 处理其他异常
            context = {
                "unexpected_error": str(e),
                "error_type": type(e).__name__,
                "response_available": response is not None
            }
            
            error_msg = f"状态码和节点ID断言时发生意外错误: {str(e)}"
            raise EnhancedAssertionError(error_msg, context)

    @staticmethod
    def assert_contains(content: Any, expected: Any = '0x', field_path: str = None):
        """
        断言包含（增强版）
        :param content: 文本内容
        :param expected: 目标文本或预期结果
        :param field_path: 字段路径（用于错误定位）
        """
        try:
            # 设置断言上下文
            default_error_reporter.set_test_context(
                assertion_type="contains",
                expected=expected,
                field_path=field_path,
                content_type=type(content).__name__
            )
            
            # 如果content是字符串，尝试解析为JSON
            if isinstance(content, str):
                try:
                    content = json.loads(content)
                except json.JSONDecodeError:
                    pass  # 保持原始字符串格式
            
            # 将content转换为字符串进行搜索
            content_str = json.dumps(content, ensure_ascii=False) if not isinstance(content, str) else content
            
            # 处理不同类型的expected参数
            if isinstance(expected, str):
                # 字符串类型：检查是否包含
                if expected not in content_str:
                    context = {
                        "expected_string": expected,
                        "content_preview": content_str[:500] + "..." if len(content_str) > 500 else content_str,
                        "content_length": len(content_str),
                        "search_performed": f"在内容中搜索 '{expected}'"
                    }
                    
                    error_msg = (
                        f"字符串包含断言失败\n"
                        f"期望包含: '{expected}'\n"
                        f"实际内容: {content_str[:200]}...\n"
                        f"内容长度: {len(content_str)} 字符"
                    )
                    
                    raise EnhancedAssertionError(error_msg, context)
                
                logger.debug(f"✅ 字符串包含断言通过: '{expected}' 在内容中找到")
                
            elif isinstance(expected, dict):
                # 字典类型：检查键值对
                missing_keys = []
                mismatched_values = []
                
                for key, value in expected.items():
                    if isinstance(content, dict):
                        if key not in content:
                            missing_keys.append(key)
                        elif value is not None and content[key] != value:
                            mismatched_values.append({
                                'key': key,
                                'expected': value,
                                'actual': content[key]
                            })
                    else:
                        if f'"{key}"' not in content_str:
                            missing_keys.append(key)
                        elif value is not None and str(value) not in content_str:
                            mismatched_values.append({
                                'key': key,
                                'expected': value,
                                'actual': '未找到匹配值'
                            })
                
                if missing_keys or mismatched_values:
                    context = {
                        "missing_keys": missing_keys,
                        "mismatched_values": mismatched_values,
                        "expected_dict": expected,
                        "actual_content": content if isinstance(content, dict) else content_str[:500],
                        "available_keys": list(content.keys()) if isinstance(content, dict) else "非字典类型"
                    }
                    
                    error_msg = f"字典包含断言失败\n"
                    if missing_keys:
                        error_msg += f"缺少键: {missing_keys}\n"
                    if mismatched_values:
                        error_msg += f"值不匹配: {mismatched_values}\n"
                    error_msg += f"可用键: {list(content.keys()) if isinstance(content, dict) else '非字典类型'}"
                    
                    raise EnhancedAssertionError(error_msg, context)
                
                logger.debug(f"✅ 字典包含断言通过: 所有键值对都匹配")
                
            elif isinstance(expected, list):
                # 列表类型：检查每个元素
                failed_items = []
                
                for i, item in enumerate(expected):
                    try:
                        EnhancedAssertionUtils.assert_contains(content, item, f"{field_path}[{i}]" if field_path else f"[{i}]")
                    except EnhancedAssertionError as e:
                        failed_items.append({
                            'index': i,
                            'item': item,
                            'error': str(e)
                        })
                
                if failed_items:
                    context = {
                        "failed_items": failed_items,
                        "expected_list": expected,
                        "total_items": len(expected),
                        "failed_count": len(failed_items)
                    }
                    
                    error_msg = (
                        f"列表包含断言失败\n"
                        f"失败项目数: {len(failed_items)}/{len(expected)}\n"
                        f"失败详情: {failed_items}"
                    )
                    
                    raise EnhancedAssertionError(error_msg, context)
                
                logger.debug(f"✅ 列表包含断言通过: 所有项目都在内容中找到")
            
            else:
                # 其他类型：转换为字符串检查
                expected_str = str(expected)
                if expected_str not in content_str:
                    context = {
                        "expected_value": expected,
                        "expected_string": expected_str,
                        "content_preview": content_str[:500],
                        "value_type": type(expected).__name__
                    }
                    
                    error_msg = (
                        f"值包含断言失败\n"
                        f"期望值: {expected} (类型: {type(expected).__name__})\n"
                        f"转换为字符串: '{expected_str}'\n"
                        f"实际内容: {content_str[:200]}..."
                    )
                    
                    raise EnhancedAssertionError(error_msg, context)
                
                logger.debug(f"✅ 值包含断言通过: '{expected_str}' 在内容中找到")
                
        except EnhancedAssertionError:
            raise
        except Exception as e:
            context = {
                "unexpected_error": str(e),
                "error_type": type(e).__name__,
                "expected": expected,
                "content_type": type(content).__name__
            }
            
            error_msg = f"包含断言时发生意外错误: {str(e)}"
            raise EnhancedAssertionError(error_msg, context)

    @staticmethod
    def assert_id_and_version(content: Any):
        """
        断言响应id和version（增强版）
        :param content: 文本内容
        """
        try:
            # 设置断言上下文
            default_error_reporter.set_test_context(
                assertion_type="id_and_version",
                content_type=type(content).__name__
            )
            
            # 如果content是字符串，尝试解析为JSON
            if isinstance(content, str):
                try:
                    content = json.loads(content)
                except json.JSONDecodeError as e:
                    context = {
                        "error": "JSON解析失败",
                        "json_error": str(e),
                        "content_preview": content[:500] if content else "空内容"
                    }
                    
                    error_msg = (
                        f"ID和版本断言失败: 无法解析JSON内容\n"
                        f"JSON错误: {str(e)}\n"
                        f"内容预览: {content[:200] if content else '空内容'}..."
                    )
                    
                    raise EnhancedAssertionError(error_msg, context)
            
            # 检查是否为字典类型
            if not isinstance(content, dict):
                context = {
                    "expected_type": "dict",
                    "actual_type": type(content).__name__,
                    "content": str(content)[:500] if content else "None"
                }
                
                error_msg = (
                    f"ID和版本断言失败: 内容不是字典类型\n"
                    f"期望类型: dict\n"
                    f"实际类型: {type(content).__name__}\n"
                    f"内容: {str(content)[:200] if content else 'None'}..."
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            # 断言id字段存在
            if 'id' not in content:
                context = {
                    "missing_field": "id",
                    "available_fields": list(content.keys()),
                    "content": content
                }
                
                error_msg = (
                    f"ID和版本断言失败: 响应中缺少 'id' 字段\n"
                    f"可用字段: {list(content.keys())}\n"
                    f"响应内容: {json.dumps(content, indent=2, ensure_ascii=False)[:300]}..."
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            # 断言jsonrpc字段存在且值正确
            if 'jsonrpc' not in content:
                context = {
                    "missing_field": "jsonrpc",
                    "available_fields": list(content.keys()),
                    "content": content
                }
                
                error_msg = (
                    f"ID和版本断言失败: 响应中缺少 'jsonrpc' 字段\n"
                    f"可用字段: {list(content.keys())}\n"
                    f"响应内容: {json.dumps(content, indent=2, ensure_ascii=False)[:300]}..."
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            if content['jsonrpc'] != '2.0':
                context = {
                    "field": "jsonrpc",
                    "expected_value": "2.0",
                    "actual_value": content['jsonrpc'],
                    "content": content
                }
                
                error_msg = (
                    f"ID和版本断言失败: jsonrpc版本不正确\n"
                    f"期望版本: '2.0'\n"
                    f"实际版本: '{content['jsonrpc']}'\n"
                    f"完整响应: {json.dumps(content, indent=2, ensure_ascii=False)[:300]}..."
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            logger.debug(f"✅ ID和版本断言通过: id={content.get('id')}, jsonrpc={content.get('jsonrpc')}")
            
        except EnhancedAssertionError:
            raise
        except Exception as e:
            context = {
                "unexpected_error": str(e),
                "error_type": type(e).__name__,
                "content_type": type(content).__name__
            }
            
            error_msg = f"ID和版本断言时发生意外错误: {str(e)}"
            raise EnhancedAssertionError(error_msg, context)

    @staticmethod
    def assert_not_contains(content: Any, expected: str = 'error'):
        """
        断言响应文本不包含（增强版）
        :param content: 文本内容
        :param expected: 不应包含的目标文本
        """
        try:
            # 设置断言上下文
            default_error_reporter.set_test_context(
                assertion_type="not_contains",
                expected_not_to_contain=expected,
                content_type=type(content).__name__
            )
            
            # 将content转换为字符串
            if isinstance(content, dict):
                content_str = json.dumps(content, ensure_ascii=False)
            elif isinstance(content, str):
                content_str = content
            else:
                content_str = str(content)
            
            # 断言不包含指定内容
            if expected in content_str:
                # 找到包含位置的上下文
                index = content_str.find(expected)
                start = max(0, index - 50)
                end = min(len(content_str), index + len(expected) + 50)
                context_snippet = content_str[start:end]
                
                context = {
                    "forbidden_text": expected,
                    "found_at_index": index,
                    "context_snippet": context_snippet,
                    "content_length": len(content_str),
                    "content_preview": content_str[:300] + "..." if len(content_str) > 300 else content_str
                }
                
                error_msg = (
                    f"不包含断言失败: 内容中发现了不应存在的文本\n"
                    f"不应包含: '{expected}'\n"
                    f"发现位置: 第 {index} 个字符\n"
                    f"上下文: ...{context_snippet}...\n"
                    f"完整内容长度: {len(content_str)} 字符"
                )
                
                raise EnhancedAssertionError(error_msg, context)
            
            logger.debug(f"✅ 不包含断言通过: '{expected}' 未在内容中找到")
            
        except EnhancedAssertionError:
            raise
        except Exception as e:
            context = {
                "unexpected_error": str(e),
                "error_type": type(e).__name__,
                "expected": expected,
                "content_type": type(content).__name__
            }
            
            error_msg = f"不包含断言时发生意外错误: {str(e)}"
            raise EnhancedAssertionError(error_msg, context)


# 创建默认的增强断言工具实例
default_enhanced_assertion_utils = EnhancedAssertionUtils()


# 提供便捷的函数接口
def assert_status_and_nodeid(response: httpx.Response, expected_status: int = 200):
    """断言响应码和节点id的便捷函数（增强版）"""
    return default_enhanced_assertion_utils.assert_status_and_nodeid(response, expected_status)


def assert_contains(content: Any, expected: Any = '0x', field_path: str = None):
    """断言包含的便捷函数（增强版）"""
    return default_enhanced_assertion_utils.assert_contains(content, expected, field_path)


def assert_id_and_version(content: Any):
    """断言响应id和version的便捷函数（增强版）"""
    return default_enhanced_assertion_utils.assert_id_and_version(content)


def assert_not_contains(content: Any, expected: str = 'error'):
    """断言不包含的便捷函数（增强版）"""
    return default_enhanced_assertion_utils.assert_not_contains(content, expected)
