"""处理配置加载和解析"""
import re
from urllib.parse import urlparse, urlunparse
import yaml
from loguru import logger
from common.handle_path import CONFIG_DIR


def load_yaml(file_path):
    """加载 YAML 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        logger.error(f'配置文件 {file_path} 未找到')
        return None
    except yaml.YAMLError as e:
        logger.error(f'配置文件 {file_path} 解析错误: {e}')
        return None


def get_config_by_env(env_name):
    """根据环境名称获取配置"""
    config_data = load_yaml(CONFIG_DIR)
    if not config_data:
        return {}

    env_config = config_data.get('url', {}).get(env_name)
    if env_config is not None:
        logger.info(f'成功加载预定义测试环境：{env_name}')
        return env_config
    else:
        logger.warning(f'测试环境参数 "{env_name}" 不是有效的预定义环境名称')
        return {}


def get_config_by_custom_url(custom_url_input):
    """根据自定义 URL 或 IP 获取并替换配置"""
    config_data = load_yaml(CONFIG_DIR)
    if not config_data:
        return {}

    # 默认加载 alphanet 环境的配置进行替换
    base_config = config_data.get('url', {}).get('alphanet', {})
    if not isinstance(base_config, dict):
        logger.warning(f'配置文件 {CONFIG_DIR} 中 \'url.alphanet\' 结构无效或不存在，无法进行域名替换')
        return {}

    processed_url = _process_custom_url(custom_url_input)
    if not processed_url:
        return {}

    try:
        target_parsed = urlparse(processed_url)
        target_hostname = target_parsed.hostname
        if not target_hostname:
            raise ValueError("无法从提供的输入解析主机名")
    except ValueError as e:
        logger.error(f'处理输入 "{custom_url_input}" 时出错: {e}')
        return {}

    logger.info(f'将使用自定义主机名 {target_hostname} 替换 alphanet 环境中的 URL')
    replaced_config = _replace_hostname_in_urls(base_config, target_hostname)
    replaced_config['default'] = processed_url  # 保留处理后的 URL 作为默认值
    return replaced_config


def _process_custom_url(url_input):
    """处理自定义 URL 或 IP，添加必要的协议前缀"""
    if url_input.startswith(('http://', 'https://')):
        return url_input
    if re.fullmatch(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', url_input):
        processed = f'https://{url_input}'
        logger.info(f'检测到IP地址输入，处理后URL为: {processed}')
        return processed
    # 可以在这里添加其他格式的检查，例如域名
    # 默认认为可能是域名，添加 https://
    if '.' in url_input:
        return f'https://{url_input}'
    return None


def _replace_hostname_in_urls(config_level, target_hostname):
    """递归替换 URL 中的主机名"""
    new_level = {}
    for key, value in config_level.items():
        if isinstance(value, str) and value.startswith(('http://', 'https://', 'ws://', 'wss://')):
            try:
                original_parsed = urlparse(value)
                new_netloc = target_hostname
                if original_parsed.port:
                    new_netloc += f":{original_parsed.port}"

                new_url = urlunparse((
                    original_parsed.scheme, new_netloc, original_parsed.path,
                    original_parsed.params, original_parsed.query, original_parsed.fragment
                ))
                new_level[key] = new_url
            except Exception as e:
                logger.warning(f'解析或重建URL "{value}" 时出错: {e}. 保留原始值。')
                new_level[key] = value
        elif isinstance(value, dict):
            new_level[key] = _replace_hostname_in_urls(value, target_hostname)
        else:
            new_level[key] = value
    return new_level


def is_custom_url(env_input):
    """判断输入是否为 URL 或 IP"""
    # 简单判断：包含点号或符合IP格式，或以协议开头
    return (
        '.' in env_input or
        re.fullmatch(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', env_input) or
        env_input.startswith(('http://', 'https://'))
    )