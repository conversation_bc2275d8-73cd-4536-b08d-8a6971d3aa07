"""
测试装饰器
自动收集测试上下文和错误信息
"""

import functools
import inspect
import time
from typing import Any, Callable
from loguru import logger
from common.error_reporter import get_error_reporter
from common.enhanced_assertion_utils import EnhancedAssertionError


def enhanced_test(test_name: str = None, chain_name: str = None, method_name: str = None):
    """
    增强测试装饰器
    自动收集测试上下文，提供详细的错误报告
    
    :param test_name: 测试名称
    :param chain_name: 链名称
    :param method_name: 方法名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 获取测试函数信息
            actual_test_name = test_name or func.__name__
            module_name = func.__module__
            
            # 尝试从参数中获取更多上下文
            test_context = {
                'test_name': actual_test_name,
                'module_name': module_name,
                'function_name': func.__name__,
                'chain_name': chain_name,
                'method_name': method_name
            }
            
            # 从函数参数中提取上下文
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 提取常见的测试参数
            if 'env' in bound_args.arguments:
                test_context['environment'] = 'test_env'
            
            if 'data' in bound_args.arguments:
                data = bound_args.arguments['data']
                if isinstance(data, dict):
                    if 'payload' in data:
                        payload = data['payload']
                        test_context['rpc_method'] = payload.get('method')
                        test_context['rpc_params'] = payload.get('params')
                        test_context['rpc_id'] = payload.get('id')
                    if 'expected' in data:
                        test_context['expected_result'] = data['expected']
            
            # 获取适合当前测试的错误报告器
            error_reporter = get_error_reporter(chain_name=chain_name, env_name='test')

            # 设置测试上下文
            error_reporter.set_test_context(**test_context)

            # 清空之前的请求历史
            error_reporter.clear_history()
            
            start_time = time.time()
            
            try:
                logger.info("=" * 80)
                logger.info(f"🧪 开始测试: {actual_test_name}")
                logger.info("=" * 80)
                logger.info(f"📋 测试上下文:")
                for key, value in test_context.items():
                    if value is not None:
                        logger.info(f"   {key}: {value}")
                
                # 执行测试函数
                result = func(*args, **kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                
                logger.info("=" * 80)
                logger.info(f"✅ 测试通过: {actual_test_name}")
                logger.info(f"⏱️  执行时间: {execution_time:.3f}秒")
                logger.info("=" * 80)
                
                return result
                
            except EnhancedAssertionError as e:
                # 处理增强断言错误
                execution_time = time.time() - start_time
                
                logger.error("=" * 80)
                logger.error(f"❌ 测试失败: {actual_test_name}")
                logger.error(f"⏱️  执行时间: {execution_time:.3f}秒")
                logger.error("=" * 80)
                
                # 记录详细的错误信息
                logger.error(f"🚨 断言错误: {str(e)}")
                if hasattr(e, 'context') and e.context:
                    logger.error("📋 错误上下文:")
                    for key, value in e.context.items():
                        logger.error(f"   {key}: {value}")
                
                # 重新抛出异常
                raise
                
            except Exception as e:
                # 处理其他异常
                execution_time = time.time() - start_time
                
                logger.error("=" * 80)
                logger.error(f"❌ 测试失败: {actual_test_name}")
                logger.error(f"⏱️  执行时间: {execution_time:.3f}秒")
                logger.error("=" * 80)
                
                # 生成详细的错误报告
                error_report = error_reporter.format_error_report(e)
                logger.error(error_report)
                
                # 重新抛出异常
                raise
        
        return wrapper
    return decorator


def api_test(chain_name: str, method_name: str):
    """
    API测试装饰器
    专门用于API测试的装饰器
    
    :param chain_name: 链名称
    :param method_name: API方法名称
    """
    def decorator(func: Callable) -> Callable:
        test_name = f"{chain_name}_{method_name}_test"
        return enhanced_test(test_name=test_name, chain_name=chain_name, method_name=method_name)(func)
    
    return decorator


def benchmark_test(expected_max_time: float = None):
    """
    性能基准测试装饰器
    
    :param expected_max_time: 期望的最大执行时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 检查性能基准
                if expected_max_time and execution_time > expected_max_time:
                    logger.warning(
                        f"⚠️  性能警告: {func.__name__} 执行时间 {execution_time:.3f}秒 "
                        f"超过期望的 {expected_max_time:.3f}秒"
                    )
                else:
                    logger.info(f"⚡ 性能良好: {func.__name__} 执行时间 {execution_time:.3f}秒")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"💥 性能测试失败: {func.__name__} 在 {execution_time:.3f}秒后失败")
                raise
        
        return wrapper
    return decorator


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    失败重试装饰器
    
    :param max_retries: 最大重试次数
    :param delay: 初始延迟时间（秒）
    :param backoff: 延迟时间倍数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    if attempt > 0:
                        logger.info(f"🔄 重试 {attempt}/{max_retries}: {func.__name__}")
                        time.sleep(current_delay)
                        current_delay *= backoff
                    
                    return func(*args, **kwargs)
                    
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(f"⚠️  尝试 {attempt + 1} 失败: {str(e)}")
                    else:
                        logger.error(f"❌ 所有重试都失败了")
            
            # 所有重试都失败，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator


def skip_on_condition(condition: Callable[[], bool], reason: str = "条件不满足"):
    """
    条件跳过装饰器
    
    :param condition: 跳过条件函数
    :param reason: 跳过原因
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            if condition():
                logger.info(f"⏭️  跳过测试 {func.__name__}: {reason}")
                import pytest
                pytest.skip(reason)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def log_test_data(log_request: bool = True, log_response: bool = True, log_assertions: bool = True):
    """
    测试数据日志装饰器
    
    :param log_request: 是否记录请求数据
    :param log_response: 是否记录响应数据
    :param log_assertions: 是否记录断言详情
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 获取默认错误报告器并设置日志标志
            error_reporter = get_error_reporter()
            error_reporter.set_test_context(
                log_request=log_request,
                log_response=log_response,
                log_assertions=log_assertions
            )

            return func(*args, **kwargs)

        return wrapper
    return decorator


# 组合装饰器示例
def comprehensive_api_test(chain_name: str, method_name: str, max_time: float = 10.0, retries: int = 2):
    """
    综合API测试装饰器
    结合多个装饰器的功能
    
    :param chain_name: 链名称
    :param method_name: API方法名称
    :param max_time: 最大执行时间
    :param retries: 重试次数
    """
    def decorator(func: Callable) -> Callable:
        # 应用多个装饰器
        decorated_func = func
        decorated_func = retry_on_failure(max_retries=retries)(decorated_func)
        decorated_func = benchmark_test(expected_max_time=max_time)(decorated_func)
        decorated_func = api_test(chain_name=chain_name, method_name=method_name)(decorated_func)
        decorated_func = log_test_data()(decorated_func)
        
        return decorated_func
    
    return decorator
