"""
配置管理器模块

实现配置缓存和热更新机制，避免频繁的文件IO操作，提升性能。
支持pytest-xdist并发执行和多进程安全。
"""

import os
import time
import threading
import fcntl
import tempfile
import pickle
from typing import Dict, Any, Optional, List
from pathlib import Path
import yaml
from loguru import logger
from common.handle_path import CONFIG_DIR, CASE_DIR
from common.types import ConfigDict, TestCaseData, PathLike


class ConfigManager:
    """
    配置管理器 - 单例模式

    提供配置缓存、热更新、文件监控等功能。使用单例模式确保全局唯一实例，
    支持多线程安全和进程间共享缓存。

    Features:
        - 单例模式：全局唯一的配置管理器实例
        - 配置缓存：避免重复读取相同配置文件
        - 热更新：自动检测文件变更并更新缓存
        - 线程安全：支持多线程并发访问
        - 进程安全：支持pytest-xdist多进程执行

    Attributes:
        _instance: 单例实例
        _lock: 线程锁，确保单例创建的线程安全
        _config_cache: 配置文件缓存字典
        _file_timestamps: 文件时间戳缓存
        _cache_lock: 缓存操作锁
    """

    _instance: Optional['ConfigManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化配置管理器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._config_cache: Dict[str, Any] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._cache_lock = threading.RLock()
        
        # 预加载主要配置文件
        self._preload_configs()
        
        logger.info("配置管理器初始化完成")
    
    def _preload_configs(self):
        """预加载主要配置文件"""
        try:
            # 加载主配置文件
            self.get_config(CONFIG_DIR)
            # 加载测试用例数据
            self.get_config(CASE_DIR)
            logger.info("主要配置文件预加载完成")
        except Exception as e:
            logger.error(f"预加载配置文件失败: {e}")
    
    def get_config(self, file_path: str, force_reload: bool = False) -> Optional[Dict[str, Any]]:
        """
        获取配置文件内容，支持缓存和热更新
        
        :param file_path: 配置文件路径
        :param force_reload: 是否强制重新加载
        :return: 配置字典或None
        """
        with self._cache_lock:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"配置文件不存在: {file_path}")
                return None
            
            # 获取文件修改时间
            current_mtime = os.path.getmtime(file_path)
            cached_mtime = self._file_timestamps.get(file_path, 0)
            
            # 检查是否需要重新加载
            if (force_reload or 
                file_path not in self._config_cache or 
                current_mtime > cached_mtime):
                
                try:
                    # 重新加载配置文件
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                    
                    # 更新缓存
                    self._config_cache[file_path] = config_data
                    self._file_timestamps[file_path] = current_mtime
                    
                    logger.debug(f"配置文件已重新加载: {file_path}")
                    
                except Exception as e:
                    logger.error(f"加载配置文件失败 {file_path}: {e}")
                    # 如果加载失败，返回缓存的版本（如果有的话）
                    return self._config_cache.get(file_path)
            
            return self._config_cache.get(file_path)
    
    def get_headers(self) -> Dict[str, Any]:
        """
        获取请求头配置
        
        :return: 请求头字典
        """
        config = self.get_config(CONFIG_DIR)
        if config and 'request_headers' in config and 'headers' in config['request_headers']:
            return config['request_headers']['headers']
        
        logger.warning("无法获取请求头配置，返回空字典")
        return {}
    
    def get_case_data(self) -> Dict[str, Any]:
        """
        获取测试用例数据
        
        :return: 测试用例数据字典
        """
        case_data = self.get_config(CASE_DIR)
        if case_data is None:
            logger.warning("无法获取测试用例数据，返回空字典")
            return {}
        return case_data
    
    def get_url_config(self, env_name: str = 'alphanet') -> Dict[str, Any]:
        """
        获取指定环境的URL配置
        
        :param env_name: 环境名称
        :return: URL配置字典
        """
        config = self.get_config(CONFIG_DIR)
        if config and 'url' in config and env_name in config['url']:
            return config['url'][env_name]
        
        logger.warning(f"无法获取环境 {env_name} 的URL配置，返回空字典")
        return {}
    
    def reload_config(self, file_path: str = None):
        """
        手动重新加载配置文件
        
        :param file_path: 指定文件路径，None表示重新加载所有缓存的配置
        """
        with self._cache_lock:
            if file_path:
                # 重新加载指定文件
                self.get_config(file_path, force_reload=True)
                logger.info(f"已重新加载配置文件: {file_path}")
            else:
                # 重新加载所有缓存的配置
                for cached_file in list(self._config_cache.keys()):
                    self.get_config(cached_file, force_reload=True)
                logger.info("已重新加载所有缓存的配置文件")
    
    def clear_cache(self):
        """清空配置缓存"""
        with self._cache_lock:
            self._config_cache.clear()
            self._file_timestamps.clear()
            logger.info("配置缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        :return: 缓存信息字典
        """
        with self._cache_lock:
            return {
                'cached_files': list(self._config_cache.keys()),
                'cache_size': len(self._config_cache),
                'file_timestamps': self._file_timestamps.copy()
            }


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """
    获取配置管理器实例
    
    :return: ConfigManager实例
    """
    return config_manager
