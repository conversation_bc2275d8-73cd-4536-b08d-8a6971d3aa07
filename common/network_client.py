"""
网络客户端模块
专门处理HTTP和WebSocket网络通信
增强的错误报告和详细日志
"""

import json
import time
import httpx
from loguru import logger
from websocket import create_connection
from common.ip_mode_handler import IPModeHandler
from common.error_reporter import get_error_reporter


class NetworkClient:
    """
    网络客户端类
    提供HTTP和WebSocket通信功能
    """
    
    def __init__(self, ip_mode_handler: IPModeHandler = None):
        """
        初始化网络客户端
        :param ip_mode_handler: IP模式处理器
        """
        self.ip_mode_handler = ip_mode_handler
    
    def send_http(self, data: dict):
        """
        发送HTTP请求（增强版，包含详细的错误报告）
        :param data: 请求数据, {'method': 'get', 'url': 'xxx', 'headers': {}, 'params': {}, 'json': {}, 'data': {}}
        :return: response
        """
        start_time = time.time()
        response = None

        try:
            # 处理IP模式
            data_to_send = self._process_ip_mode(data)

            # 获取错误报告器并记录详细的请求日志
            error_reporter = get_error_reporter()
            error_reporter.log_request(
                data_to_send.get('method', 'get'),
                data_to_send.get('url'),
                data_to_send.get('headers'),
                data_to_send.get('params'),
                data_to_send.get('json'),
                data_to_send.get('data')
            )

            # 设置SSL验证
            verify_ssl = data_to_send.pop('verify', True)

            # 发送请求
            with httpx.Client(verify=verify_ssl, timeout=30) as client:
                response = client.request(**data_to_send)

            # 计算请求耗时
            duration = time.time() - start_time

            # 记录详细的响应日志
            error_reporter.log_response(response, duration)

            return response

        except Exception as e:
            # 计算请求耗时（即使失败）
            duration = time.time() - start_time

            # 生成详细的错误报告
            error_report = error_reporter.format_error_report(e, response)
            logger.error(error_report)

            # 记录失败的请求详情
            logger.error(f"请求失败，耗时: {duration:.3f}秒")

            raise e

    def websocket_connection(self, url):
        """
        建立WebSocket连接
        :param url: 连接地址
        :return: WebSocket连接对象
        """
        try:
            logger.info(f"建立WebSocket连接: {url}")
            ws = create_connection(url)
            return ws
        except Exception as e:
            logger.exception(f'发生的错误为：{e}')
            raise e

    def send_websocket(self, ws, data):
        """
        发送WebSocket消息
        :param ws: WebSocket连接对象
        :param data: 请求数据
        :return: 响应数据
        """
        try:
            logger.info(f"发送WebSocket消息: {data}")
            ws.send(json.dumps(data))
            response = json.loads(ws.recv())
            logger.info(f"WebSocket响应: {response}")
            return response
        except Exception as e:
            logger.exception(f'WebSocket通信错误：{e}')
            raise e

    def _process_ip_mode(self, data: dict) -> dict:
        """
        处理IP模式
        :param data: 原始请求数据
        :return: 处理后的请求数据
        """
        if self.ip_mode_handler is None:
            return data.copy()
        
        # 使用IP模式处理器处理请求
        processed_data = self.ip_mode_handler.process_request(data)
        return processed_data

    def _log_request(self, method, url, headers=None, params=None, json_data=None, data=None):
        """
        记录请求日志
        :param method: 请求方式
        :param url: 请求地址
        :param headers: 请求头
        :param params: 请求参数
        :param json_data: JSON请求体
        :param data: 表单请求体
        """
        logger.info(f"请求方式：{method}")
        logger.info(f"请求地址：{url}")
        logger.info(f"请求头：{headers}")
        logger.info(f"请求参数：{params}")
        logger.info(f"请求体(json)：{json_data}")
        logger.info(f"请求体(data)：{data}")


# 创建默认的网络客户端实例
default_network_client = NetworkClient()


# 提供便捷的函数接口，保持向后兼容
def send_http(data: dict):
    """发送HTTP请求的便捷函数"""
    return default_network_client.send_http(data)


def websocket_connection(url):
    """建立WebSocket连接的便捷函数"""
    return default_network_client.websocket_connection(url)


def send_websocket(ws, data):
    """发送WebSocket消息的便捷函数"""
    return default_network_client.send_websocket(ws, data)
