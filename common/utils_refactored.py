"""
重构后的工具类模块 - 兼容层
保持向后兼容性，同时将职责分离到专门的模块中
"""

# 导入分离后的模块
from common.network_client import NetworkClient
from common.assertion_utils import AssertionUtils
from common.template_utils import TemplateUtils
from common.enhanced_config_manager import EnhancedConfigManager
from common.crypto_utils import CryptoUtils

# 保持原有导入以支持现有代码
import yaml
import os
from urllib.parse import urlparse
from loguru import logger
from common.handle_path import CONFIG_DIR
from common.ip_mode_handler import IPModeHandler


class Utils:
    """
    重构后的工具类 - 兼容层
    保持向后兼容性，将具体功能委托给专门的模块
    """

    # 初始化各个专门的工具类实例
    _network_client = NetworkClient()
    _assertion_utils = AssertionUtils()
    _template_utils = TemplateUtils()
    _config_manager = EnhancedConfigManager()
    _crypto_utils = CryptoUtils()
    _ip_mode_handler = None

    # ============ IP模式处理相关方法 ============
    
    @classmethod
    def _load_config_and_initialize_ip_handler(cls):
        """加载配置文件并初始化IP模式处理器"""
        # 使用配置管理器获取配置
        config_data = cls._config_manager.load_yaml(CONFIG_DIR)
        if not config_data:
            logger.error(f"无法获取配置文件: {CONFIG_DIR}")
            config_data = {}

        # 初始化IP模式处理器
        if cls._ip_mode_handler is None:
            url_paths_mapping = cls._build_url_paths_mapping(config_data)
            cls._ip_mode_handler = IPModeHandler(url_paths_mapping)
            # 设置网络客户端的IP模式处理器
            cls._network_client.ip_mode_handler = cls._ip_mode_handler
    
    @classmethod
    def _build_url_paths_mapping(cls, config_data=None):
        """构建URL路径到链名的映射"""
        if config_data is None:
            config_data = cls._config_manager.load_yaml(CONFIG_DIR) or {}
        
        url_paths = {}
        if 'url' in config_data and 'alphanet' in config_data['url']:
            alphanet_config = config_data['url']['alphanet']

            def extract_paths(config_level, base_name=None):
                for key, value in config_level.items():
                    current_name = f"{base_name}_{key}" if base_name else key
                    if isinstance(value, str) and value.startswith(('http://', 'https://')):
                        try:
                            parsed_url = urlparse(value)
                            path = parsed_url.path.rstrip('/')
                            if path:
                                url_paths[path] = current_name
                                logger.trace(f"Config Path Map: '{path}' -> '{current_name}'")
                        except Exception as e:
                            logger.warning(f"解析配置文件 URL '{value}' 失败: {e}")
                    elif isinstance(value, dict):
                        extract_paths(value, current_name)

            extract_paths(alphanet_config)
        return url_paths

    # ============ 网络相关方法（委托给NetworkClient） ============
    
    @classmethod
    def send_http(cls, data: dict):
        """
        发送HTTP请求
        :param data: 请求数据
        :return: response
        """
        # 确保IP模式处理器已初始化
        if cls._ip_mode_handler is None:
            cls._load_config_and_initialize_ip_handler()
        
        return cls._network_client.send_http(data)

    @classmethod
    def websocket_connection(cls, url):
        """
        建立WebSocket连接
        :param url: 连接地址
        :return: WebSocket连接对象
        """
        return cls._network_client.websocket_connection(url)

    @classmethod
    def send_websocket(cls, ws, data):
        """
        发送WebSocket消息
        :param ws: WebSocket连接对象
        :param data: 请求数据
        :return: 响应数据
        """
        return cls._network_client.send_websocket(ws, data)

    # ============ 配置管理相关方法（委托给EnhancedConfigManager） ============
    
    @classmethod
    def handle_yaml(cls, file_name):
        """
        读取yaml文件 - 支持pytest-xdist并发执行的缓存机制
        :param file_name: 文件路径
        :return: YAML数据
        """
        return cls._config_manager.load_yaml(file_name)

    @classmethod
    def reload_config(cls, file_name=None):
        """
        手动重新加载配置文件（支持进程间同步）
        :param file_name: 指定文件路径，None表示重新加载所有缓存的配置
        """
        return cls._config_manager.reload_config(file_name)

    @classmethod
    def clear_config_cache(cls):
        """清空配置缓存（包括共享缓存）"""
        return cls._config_manager.clear_cache()

    @classmethod
    def get_cache_info(cls):
        """
        获取缓存信息（包括共享缓存）
        :return: 缓存信息字典
        """
        return cls._config_manager.get_cache_info()

    @staticmethod
    def read_yaml(file_path):
        """
        读取yaml文件（简单版本，不使用缓存）
        :param file_path: yaml文件路径
        :return: YAML数据
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f'读取yaml文件失败: {file_path}, 错误: {e}')
            raise e

    # ============ 断言相关方法（委托给AssertionUtils） ============
    
    @staticmethod
    def assert_status_and_nodeid(response):
        """
        断言响应码和节点id
        :param response: 响应对象
        """
        return AssertionUtils.assert_status_and_nodeid(response)

    @staticmethod
    def assert_contains(content, expected='0x'):
        """
        断言包含，默认参数0x
        :param content: 文本内容
        :param expected: 目标文本
        """
        return AssertionUtils.assert_contains(content, expected)

    @staticmethod
    def assert_id_and_version(content):
        """
        断言响应id和version
        :param content: 文本内容
        """
        return AssertionUtils.assert_id_and_version(content)

    @staticmethod
    def assert_not_contains(content, expected='error'):
        """
        断言响应文本不包含
        :param content: 文本内容
        :param expected: 目标文本
        """
        return AssertionUtils.assert_not_contains(content, expected)

    # ============ 模板处理相关方法（委托给TemplateUtils） ============
    
    @classmethod
    def handle_template(cls, source_data, replace_data: dict):
        """
        替换文本变量
        :param source_data: 源数据
        :param replace_data: 需要替换的内容
        :return: 替换后的数据
        """
        return cls._template_utils.handle_template(source_data, replace_data)

    # ============ 加密工具相关方法（委托给CryptoUtils） ============
    
    @staticmethod
    def tx_decoder(encoded_tx):
        """
        解码交易并计算哈希值
        :param encoded_tx: base64编码的交易数据
        :return: 交易哈希值
        """
        return CryptoUtils.tx_decoder(encoded_tx)


# ============ 向后兼容的便捷函数 ============

# 网络相关
def send_http(data: dict):
    """发送HTTP请求的便捷函数"""
    return Utils.send_http(data)

def websocket_connection(url):
    """建立WebSocket连接的便捷函数"""
    return Utils.websocket_connection(url)

def send_websocket(ws, data):
    """发送WebSocket消息的便捷函数"""
    return Utils.send_websocket(ws, data)

# 配置管理相关
def handle_yaml(file_name):
    """读取YAML文件的便捷函数"""
    return Utils.handle_yaml(file_name)

def reload_config(file_name=None):
    """重新加载配置的便捷函数"""
    return Utils.reload_config(file_name)

def clear_config_cache():
    """清空配置缓存的便捷函数"""
    return Utils.clear_config_cache()

# 断言相关
def assert_status_and_nodeid(response):
    """断言响应码和节点id的便捷函数"""
    return Utils.assert_status_and_nodeid(response)

def assert_contains(content, expected='0x'):
    """断言包含的便捷函数"""
    return Utils.assert_contains(content, expected)

def assert_id_and_version(content):
    """断言响应id和version的便捷函数"""
    return Utils.assert_id_and_version(content)

def assert_not_contains(content, expected='error'):
    """断言不包含的便捷函数"""
    return Utils.assert_not_contains(content, expected)

# 模板处理相关
def handle_template(source_data, replace_data: dict):
    """处理模板的便捷函数"""
    return Utils.handle_template(source_data, replace_data)

# 加密工具相关
def tx_decoder(encoded_tx):
    """解码交易的便捷函数"""
    return Utils.tx_decoder(encoded_tx)
