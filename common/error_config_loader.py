"""
错误报告配置加载器
专门处理错误报告相关的配置加载和管理
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger
from common.handle_path import BASE_DIR


class ErrorReportingConfigLoader:
    """错误报告配置加载器"""
    
    def __init__(self):
        """初始化配置加载器"""
        self.config_path = Path(BASE_DIR) / "conf" / "error_reporting.yaml"
        self._config_cache: Optional[Dict[str, Any]] = None
        self._last_modified: Optional[float] = None
    
    def load_config(self, force_reload: bool = False) -> Dict[str, Any]:
        """
        加载错误报告配置
        
        Args:
            force_reload: 是否强制重新加载配置
            
        Returns:
            Dict[str, Any]: 错误报告配置字典
        """
        try:
            # 检查文件是否存在
            if not self.config_path.exists():
                logger.warning(f"错误报告配置文件不存在: {self.config_path}")
                return self._get_default_config()
            
            # 检查是否需要重新加载
            current_modified = self.config_path.stat().st_mtime
            if (not force_reload and 
                self._config_cache is not None and 
                self._last_modified == current_modified):
                return self._config_cache
            
            # 加载配置文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if config is None:
                logger.warning(f"错误报告配置文件为空: {self.config_path}")
                return self._get_default_config()
            
            # 缓存配置和修改时间
            self._config_cache = config
            self._last_modified = current_modified
            
            logger.debug(f"成功加载错误报告配置: {self.config_path}")
            return config
            
        except yaml.YAMLError as e:
            logger.error(f"解析错误报告配置文件失败: {e}")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"加载错误报告配置失败: {e}")
            return self._get_default_config()
    
    def get_error_reporting_config(self) -> Dict[str, Any]:
        """
        获取错误报告配置
        
        Returns:
            Dict[str, Any]: 错误报告配置
        """
        config = self.load_config()
        return config.get('error_reporting', {})
    
    def get_chain_specific_config(self, chain_name: str) -> Dict[str, Any]:
        """
        获取特定链的配置覆盖
        
        Args:
            chain_name: 链名称
            
        Returns:
            Dict[str, Any]: 链特定配置
        """
        config = self.load_config()
        chain_specific = config.get('chain_specific', {})
        return chain_specific.get(chain_name, {})
    
    def get_environment_specific_config(self, env_name: str) -> Dict[str, Any]:
        """
        获取环境特定配置
        
        Args:
            env_name: 环境名称 (development, testing, production)
            
        Returns:
            Dict[str, Any]: 环境特定配置
        """
        config = self.load_config()
        env_specific = config.get('environment_specific', {})
        return env_specific.get(env_name, {})
    
    def get_merged_config(self, chain_name: str = None, env_name: str = None) -> Dict[str, Any]:
        """
        获取合并后的配置（基础配置 + 链特定 + 环境特定）
        
        Args:
            chain_name: 链名称
            env_name: 环境名称
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        # 获取基础配置
        base_config = self.get_error_reporting_config()
        merged_config = base_config.copy()
        
        # 合并链特定配置
        if chain_name:
            chain_config = self.get_chain_specific_config(chain_name)
            merged_config = self._deep_merge(merged_config, chain_config)
        
        # 合并环境特定配置
        if env_name:
            env_config = self.get_environment_specific_config(env_name)
            if 'error_reporting' in env_config:
                merged_config = self._deep_merge(merged_config, env_config['error_reporting'])
        
        return merged_config
    
    def _deep_merge(self, base_dict: Dict[str, Any], override_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并字典
        
        Args:
            base_dict: 基础字典
            override_dict: 覆盖字典
            
        Returns:
            Dict[str, Any]: 合并后的字典
        """
        result = base_dict.copy()
        
        for key, value in override_dict.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            Dict[str, Any]: 默认错误报告配置
        """
        return {
            'error_reporting': {
                'enabled': True,
                'log_level': 'INFO',
                'request_logging': {
                    'enabled': True,
                    'log_headers': True,
                    'sanitize_sensitive_data': True,
                    'sensitive_headers': ['authorization', 'x-api-key', 'cookie', 'token']
                },
                'response_logging': {
                    'enabled': True,
                    'log_status_code': True,
                    'log_response_time': True,
                    'max_response_length': 1000
                },
                'error_categories': {
                    'network': {
                        'error_types': ['ConnectionError', 'Timeout', 'HTTPError'],
                        'suggestions': ['检查网络连接', '验证服务器状态']
                    },
                    'authentication': {
                        'status_codes': [401, 403],
                        'suggestions': ['检查API密钥', '验证认证头']
                    },
                    'server': {
                        'status_codes': [500, 502, 503, 504],
                        'suggestions': ['服务器错误，请稍后重试']
                    }
                },
                'report_format': {
                    'use_colors': True,
                    'use_emojis': True
                }
            }
        }
    
    def reload_config(self):
        """强制重新加载配置"""
        self.load_config(force_reload=True)
        logger.info("错误报告配置已重新加载")


# 创建全局配置加载器实例
_error_config_loader = None


def get_error_config_loader() -> ErrorReportingConfigLoader:
    """
    获取错误报告配置加载器实例（单例模式）
    
    Returns:
        ErrorReportingConfigLoader: 配置加载器实例
    """
    global _error_config_loader
    if _error_config_loader is None:
        _error_config_loader = ErrorReportingConfigLoader()
    return _error_config_loader


def get_error_reporting_config(chain_name: str = None, env_name: str = None) -> Dict[str, Any]:
    """
    获取错误报告配置的便捷函数
    
    Args:
        chain_name: 链名称
        env_name: 环境名称
        
    Returns:
        Dict[str, Any]: 错误报告配置
    """
    loader = get_error_config_loader()
    return loader.get_merged_config(chain_name=chain_name, env_name=env_name)
