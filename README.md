# BlockPI RPC 测试框架

一个用于测试区块链 RPC 接口的自动化测试框架。

## 📚 项目结构

apis ----> 接口层，单接口封装 \
common ----> 公共方法 \
conf ----> 配置文件 \
data ----> 测试数据 \
log ---> 日志 \
report ---> 测试报告 \
testcases ---> 测试用例 \
conftest.py ---> 前置条件处理 \
pytest.ini ---> pytest配置文件 \
requirements.txt ---> 相关依赖包文件 \
run.py ---> 测试用例运行主程序 \


## 🚀 快速开始

### 环境准备

#### 安装依赖

```bash
# 安装核心依赖
pip install -r requirements.txt
```

### 运行测试

#### 基本运行方式

运行所有测试
```bash
pytest
```

更详细的输出
```bash
pytest -vv
```

#### 指定环境和链

指定环境
```bash
pytest --env alphanet
```

指定环境和链
```bash
pytest --env testnet -m bsc
```

运行多条链
```bash
pytest -m "bsc or avax"
```

排除特定链
```bash
pytest -m "evm and not bsc"
```

运行包含特定关键字的测试
```bash
pytest -k ethereum
```

#### 多线程运行 🚀

使用4个CPU核心并行运行
```bash
pytest -n 4
```

指定链并使用多线程
```bash
pytest -m bsc -n 4
```

自动检测CPU核心数并使用全部
```bash
pytest -n auto
```

## 🔧 配置说明
测试配置位于 conf/config.yaml 文件中，可以根据需要修改以下配置：

- 环境配置（alphanet、stage、testnet）
- 超时设置
- 重试次数
- 日志级别

## 📝 测试用例编写指南
1. 在 testcases/ 目录下创建新的测试文件
2. 使用 @pytest.mark 标记测试用例所属的链
3. 使用 data/case_data.yaml 文件存储测试数据

## 🛠️ 开发工具

```bash
# 代码格式化
make format

# 代码检查
make lint

# 类型检查
make type-check

# 运行测试并生成覆盖率报告
make test-cov

# 生成文档
make docs
```

查看所有可用命令：
```bash
make help
```

