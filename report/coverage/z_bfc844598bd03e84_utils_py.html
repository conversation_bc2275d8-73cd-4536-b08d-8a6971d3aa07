<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for common/utils.py: 65.44%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>common/utils.py</b>:
            <span class="pc_cov">65.44%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">217 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">142<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">75<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">2<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_bfc844598bd03e84_types_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_bfc844598bd03e84_utils_refactored_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 14:59 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">&#24037;&#20855;&#31867;&#27169;&#22359;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">&#25552;&#20379;&#36890;&#29992;&#30340;&#24037;&#20855;&#20989;&#25968;&#65292;&#21253;&#25324;HTTP&#35831;&#27714;&#12289;&#25991;&#20214;&#22788;&#29702;&#12289;&#26029;&#35328;&#31561;&#21151;&#33021;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">string</span> <span class="key">import</span> <span class="nam">Template</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">Union</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Tuple</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">loguru</span> <span class="key">import</span> <span class="nam">logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="nam">websocket</span> <span class="key">import</span> <span class="nam">create_connection</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">import</span> <span class="nam">yaml</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">import</span> <span class="nam">json</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">import</span> <span class="nam">httpx</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">import</span> <span class="nam">base64</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">import</span> <span class="nam">hashlib</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">from</span> <span class="nam">urllib</span><span class="op">.</span><span class="nam">parse</span> <span class="key">import</span> <span class="nam">urlparse</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="nam">pathlib</span> <span class="key">import</span> <span class="nam">Path</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="key">from</span> <span class="nam">common</span><span class="op">.</span><span class="nam">handle_path</span> <span class="key">import</span> <span class="nam">CONFIG_DIR</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">from</span> <span class="nam">common</span><span class="op">.</span><span class="nam">ip_mode_handler</span> <span class="key">import</span> <span class="nam">IPModeHandler</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">from</span> <span class="nam">common</span><span class="op">.</span><span class="nam">config_manager</span> <span class="key">import</span> <span class="nam">get_config_manager</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="key">from</span> <span class="nam">common</span><span class="op">.</span><span class="nam">types</span> <span class="key">import</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">JsonValue</span><span class="op">,</span> <span class="nam">JsonDict</span><span class="op">,</span> <span class="nam">HttpPayload</span><span class="op">,</span> <span class="nam">HttpResponse</span><span class="op">,</span> <span class="nam">PathLike</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">ConfigDict</span><span class="op">,</span> <span class="nam">TestCaseData</span><span class="op">,</span> <span class="nam">RpcRequest</span><span class="op">,</span> <span class="nam">RpcResponse</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="key">class</span> <span class="nam">Utils</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t"><span class="str">    &#24037;&#20855;&#31867;&#65292;&#25552;&#20379;&#21508;&#31181;&#36890;&#29992;&#26041;&#27861;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="str">    &#36825;&#20010;&#31867;&#21253;&#21547;&#20102;&#39033;&#30446;&#20013;&#20351;&#29992;&#30340;&#25152;&#26377;&#36890;&#29992;&#24037;&#20855;&#26041;&#27861;&#65292;&#21253;&#25324;&#65306;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t"><span class="str">    - HTTP&#35831;&#27714;&#22788;&#29702;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t"><span class="str">    - &#25991;&#20214;&#35835;&#20889;&#25805;&#20316;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t"><span class="str">    - &#25968;&#25454;&#26029;&#35328;&#39564;&#35777;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t"><span class="str">    - &#23383;&#31526;&#20018;&#27169;&#26495;&#22788;&#29702;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="str">    - WebSocket&#36890;&#20449;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t"><span class="str">    - &#21152;&#23494;&#21644;&#21704;&#24076;&#35745;&#31639;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="str">    &#25152;&#26377;&#26041;&#27861;&#37117;&#26159;&#38745;&#24577;&#26041;&#27861;&#65292;&#21487;&#20197;&#30452;&#25509;&#36890;&#36807;&#31867;&#21517;&#35843;&#29992;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t"><span class="str">    Attributes:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="str">        _ip_mode_handler: IP&#27169;&#24335;&#22788;&#29702;&#22120;&#23454;&#20363;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t"><span class="str">        _config_manager: &#37197;&#32622;&#31649;&#29702;&#22120;&#23454;&#20363;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="com"># --- &#20351;&#29992;&#26032;&#30340;&#37197;&#32622;&#31649;&#29702;&#22120;&#65292;&#36991;&#20813;&#37325;&#22797;&#35835;&#21462; ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">    <span class="nam">_ip_mode_handler</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">IPModeHandler</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="nam">_config_manager</span> <span class="op">=</span> <span class="nam">get_config_manager</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="com"># --- &#32467;&#26463;&#32531;&#23384; ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="key">def</span> <span class="nam">_load_config_and_initialize_ip_handler</span><span class="op">(</span><span class="nam">cls</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t"><span class="str">        &#21152;&#36733;&#37197;&#32622;&#25991;&#20214;&#24182;&#21021;&#22987;&#21270;IP&#27169;&#24335;&#22788;&#29702;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t"><span class="str">        &#20174;&#37197;&#32622;&#31649;&#29702;&#22120;&#33719;&#21462;&#37197;&#32622;&#25968;&#25454;&#65292;&#24182;&#21021;&#22987;&#21270;IP&#27169;&#24335;&#22788;&#29702;&#22120;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t"><span class="str">        &#22914;&#26524;&#37197;&#32622;&#25991;&#20214;&#19981;&#23384;&#22312;&#25110;&#35835;&#21462;&#22833;&#36133;&#65292;&#23558;&#20351;&#29992;&#31354;&#37197;&#32622;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">        <span class="com"># &#20351;&#29992;&#37197;&#32622;&#31649;&#29702;&#22120;&#33719;&#21462;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">        <span class="nam">config_data</span> <span class="op">=</span> <span class="nam">cls</span><span class="op">.</span><span class="nam">_config_manager</span><span class="op">.</span><span class="nam">get_config</span><span class="op">(</span><span class="nam">CONFIG_DIR</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">config_data</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#26080;&#27861;&#20174;&#37197;&#32622;&#31649;&#29702;&#22120;&#33719;&#21462;&#37197;&#32622;&#25991;&#20214;: </span><span class="op">{</span><span class="nam">CONFIG_DIR</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">            <span class="nam">config_data</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">        <span class="com"># &#21021;&#22987;&#21270;IP&#27169;&#24335;&#22788;&#29702;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">        <span class="key">if</span> <span class="nam">cls</span><span class="op">.</span><span class="nam">_ip_mode_handler</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">            <span class="nam">url_paths_mapping</span> <span class="op">=</span> <span class="nam">cls</span><span class="op">.</span><span class="nam">_build_url_paths_mapping</span><span class="op">(</span><span class="nam">config_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="nam">cls</span><span class="op">.</span><span class="nam">_ip_mode_handler</span> <span class="op">=</span> <span class="nam">IPModeHandler</span><span class="op">(</span><span class="nam">url_paths_mapping</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">    <span class="key">def</span> <span class="nam">_build_url_paths_mapping</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">config_data</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">ConfigDict</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">str</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t"><span class="str">        &#26500;&#24314;URL&#36335;&#24452;&#21040;&#38142;&#21517;&#30340;&#26144;&#23556;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t"><span class="str">            config_data: &#37197;&#32622;&#25968;&#25454;&#23383;&#20856;&#65292;&#22914;&#26524;&#20026;None&#21017;&#20174;&#37197;&#32622;&#31649;&#29702;&#22120;&#33719;&#21462;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t"><span class="str">        Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t"><span class="str">            Dict[str, str]: URL&#36335;&#24452;&#21040;&#38142;&#21517;&#30340;&#26144;&#23556;&#23383;&#20856;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">        <span class="key">if</span> <span class="nam">config_data</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">            <span class="nam">config_data</span> <span class="op">=</span> <span class="nam">cls</span><span class="op">.</span><span class="nam">_config_manager</span><span class="op">.</span><span class="nam">get_config</span><span class="op">(</span><span class="nam">CONFIG_DIR</span><span class="op">)</span> <span class="key">or</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">        <span class="nam">url_paths</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="key">if</span> <span class="str">'url'</span> <span class="key">in</span> <span class="nam">config_data</span> <span class="key">and</span> <span class="str">'alphanet'</span> <span class="key">in</span> <span class="nam">config_data</span><span class="op">[</span><span class="str">'url'</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">            <span class="nam">alphanet_config</span> <span class="op">=</span> <span class="nam">config_data</span><span class="op">[</span><span class="str">'url'</span><span class="op">]</span><span class="op">[</span><span class="str">'alphanet'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">            <span class="key">def</span> <span class="nam">extract_paths</span><span class="op">(</span><span class="nam">config_level</span><span class="op">,</span> <span class="nam">base_name</span><span class="op">=</span><span class="key">None</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">                <span class="key">for</span> <span class="nam">key</span><span class="op">,</span> <span class="nam">value</span> <span class="key">in</span> <span class="nam">config_level</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">                    <span class="nam">current_name</span> <span class="op">=</span> <span class="fst">f"</span><span class="op">{</span><span class="nam">base_name</span><span class="op">}</span><span class="fst">_</span><span class="op">{</span><span class="nam">key</span><span class="op">}</span><span class="fst">"</span> <span class="key">if</span> <span class="nam">base_name</span> <span class="key">else</span> <span class="nam">key</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">                    <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">value</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span> <span class="key">and</span> <span class="nam">value</span><span class="op">.</span><span class="nam">startswith</span><span class="op">(</span><span class="op">(</span><span class="str">'http://'</span><span class="op">,</span> <span class="str">'https://'</span><span class="op">)</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">                        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">                            <span class="nam">parsed_url</span> <span class="op">=</span> <span class="nam">urlparse</span><span class="op">(</span><span class="nam">value</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">                            <span class="nam">path</span> <span class="op">=</span> <span class="nam">parsed_url</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">rstrip</span><span class="op">(</span><span class="str">'/'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">                            <span class="key">if</span> <span class="nam">path</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">                                <span class="nam">url_paths</span><span class="op">[</span><span class="nam">path</span><span class="op">]</span> <span class="op">=</span> <span class="nam">current_name</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">                                <span class="nam">logger</span><span class="op">.</span><span class="nam">trace</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Config Path Map: '</span><span class="op">{</span><span class="nam">path</span><span class="op">}</span><span class="fst">' -> '</span><span class="op">{</span><span class="nam">current_name</span><span class="op">}</span><span class="fst">'</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">                        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">                            <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35299;&#26512;&#37197;&#32622;&#25991;&#20214; URL '</span><span class="op">{</span><span class="nam">value</span><span class="op">}</span><span class="fst">' &#22833;&#36133;: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">                    <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">value</span><span class="op">,</span> <span class="nam">dict</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">                        <span class="nam">extract_paths</span><span class="op">(</span><span class="nam">value</span><span class="op">,</span> <span class="nam">current_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">            <span class="nam">extract_paths</span><span class="op">(</span><span class="nam">alphanet_config</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">        <span class="key">return</span> <span class="nam">url_paths</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">    <span class="key">def</span> <span class="nam">send_http</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">data</span><span class="op">:</span> <span class="nam">HttpPayload</span><span class="op">)</span> <span class="op">-></span> <span class="nam">HttpResponse</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t"><span class="str">        &#21457;&#36865;HTTP&#35831;&#27714;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t"><span class="str">            data: HTTP&#35831;&#27714;&#36733;&#33655;&#65292;&#21253;&#21547;&#20197;&#19979;&#23383;&#27573;&#65306;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t"><span class="str">                - method: HTTP&#26041;&#27861; ('get', 'post', 'put', 'delete', &#31561;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t"><span class="str">                - url: &#35831;&#27714;URL</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t"><span class="str">                - headers: &#35831;&#27714;&#22836;&#23383;&#20856; (&#21487;&#36873;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t"><span class="str">                - params: &#26597;&#35810;&#21442;&#25968;&#23383;&#20856; (&#21487;&#36873;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t"><span class="str">                - json: JSON&#25968;&#25454; (&#21487;&#36873;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t"><span class="str">                - data: &#34920;&#21333;&#25968;&#25454; (&#21487;&#36873;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t"><span class="str">                - timeout: &#36229;&#26102;&#26102;&#38388; (&#21487;&#36873;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t"><span class="str">                - verify: SSL&#39564;&#35777; (&#21487;&#36873;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t"><span class="str">        Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t"><span class="str">            HttpResponse: HTTP&#21709;&#24212;&#23545;&#35937;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t"><span class="str">        Raises:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t"><span class="str">            Exception: &#24403;&#35831;&#27714;&#22833;&#36133;&#26102;&#25243;&#20986;&#24322;&#24120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t"><span class="str">        Example:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t"><span class="str">            >>> payload = {</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t"><span class="str">            ...     'method': 'post',</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t"><span class="str">            ...     'url': 'https://api.example.com/rpc',</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t"><span class="str">            ...     'headers': {'Content-Type': 'application/json'},</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t"><span class="str">            ...     'json': {'jsonrpc': '2.0', 'method': 'eth_chainId', 'id': 1}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t"><span class="str">            ... }</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t"><span class="str">            >>> response = Utils.send_http(payload)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t"><span class="str">            >>> print(response.status_code)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t"><span class="str">            200</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">        <span class="com"># &#30830;&#20445;&#37197;&#32622;&#21644;IP&#27169;&#24335;&#22788;&#29702;&#22120;&#24050;&#21021;&#22987;&#21270;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="nam">cls</span><span class="op">.</span><span class="nam">_load_config_and_initialize_ip_handler</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">        <span class="nam">request_url</span> <span class="op">=</span> <span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'url'</span><span class="op">,</span> <span class="str">''</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">        <span class="nam">request_headers</span> <span class="op">=</span> <span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'headers'</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="op">.</span><span class="nam">copy</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'headers'</span><span class="op">)</span> <span class="key">else</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="nam">verify_ssl</span> <span class="op">=</span> <span class="key">True</span>  <span class="com"># &#40664;&#35748;&#21551;&#29992;SSL&#39564;&#35777;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">        <span class="nam">host_header_added</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#26159;&#21542;&#20026;IP&#27169;&#24335;&#24182;&#24212;&#29992;&#30456;&#24212;&#37197;&#32622;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="nam">is_ip_mode</span><span class="op">,</span> <span class="nam">original_env_value</span> <span class="op">=</span> <span class="nam">cls</span><span class="op">.</span><span class="nam">_ip_mode_handler</span><span class="op">.</span><span class="nam">is_ip_mode</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="key">if</span> <span class="nam">is_ip_mode</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">            <span class="nam">verify_ssl</span><span class="op">,</span> <span class="nam">host_header_added</span> <span class="op">=</span> <span class="nam">cls</span><span class="op">.</span><span class="nam">_ip_mode_handler</span><span class="op">.</span><span class="nam">apply_ip_mode_config</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">                <span class="nam">request_headers</span><span class="op">,</span> <span class="nam">request_url</span><span class="op">,</span> <span class="nam">original_env_value</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">        <span class="com"># &#20934;&#22791;&#26368;&#32456;&#21457;&#36865;&#30340;&#25968;&#25454;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">        <span class="nam">data_to_send</span> <span class="op">=</span> <span class="nam">data</span><span class="op">.</span><span class="nam">copy</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">        <span class="nam">data_to_send</span><span class="op">[</span><span class="str">'headers'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">request_headers</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="com"># &#35760;&#24405;&#35831;&#27714;&#26085;&#24535;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">        <span class="nam">cls</span><span class="op">.</span><span class="nam">__api_log</span><span class="op">(</span><span class="nam">method</span><span class="op">=</span><span class="nam">data_to_send</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'method'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">                      <span class="nam">url</span><span class="op">=</span><span class="nam">request_url</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">                      <span class="nam">headers</span><span class="op">=</span><span class="nam">request_headers</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">                      <span class="nam">params</span><span class="op">=</span><span class="nam">data_to_send</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'params'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">                      <span class="nam">json</span><span class="op">=</span><span class="nam">data_to_send</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'json'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">                      <span class="nam">data</span><span class="op">=</span><span class="nam">data_to_send</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'data'</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">            <span class="com"># &#21457;&#36865;&#35831;&#27714;&#65292;&#20351;&#29992;&#26356;&#26032;&#21518;&#30340; verify_ssl</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">            <span class="nam">response</span> <span class="op">=</span> <span class="nam">httpx</span><span class="op">.</span><span class="nam">request</span><span class="op">(</span><span class="op">**</span><span class="nam">data_to_send</span><span class="op">,</span> <span class="nam">timeout</span><span class="op">=</span><span class="num">20.0</span><span class="op">,</span> <span class="nam">verify</span><span class="op">=</span><span class="nam">verify_ssl</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#21709;&#24212;&#29366;&#24577;&#30721;: </span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">status_code</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">            <span class="key">return</span> <span class="nam">response</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">        <span class="key">except</span> <span class="nam">httpx</span><span class="op">.</span><span class="nam">RequestError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#21457;&#36865;&#35831;&#27714;&#22833;&#36133;&#65292;&#21407;&#22987;&#35831;&#27714;&#21442;&#25968;&#20026;&#65306;</span><span class="op">{</span><span class="nam">data</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#22833;&#36133;&#35831;&#27714;&#35814;&#24773;&#65288;&#21547;&#22788;&#29702;&#21518;headers&#21644;verify=</span><span class="op">{</span><span class="nam">verify_ssl</span><span class="op">}</span><span class="fst">&#65289;: </span><span class="op">{</span><span class="nam">data_to_send</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#21457;&#29983;&#30340;&#38169;&#35823;&#20026;&#65306;</span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">    <span class="key">def</span> <span class="nam">websocket_connection</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">url</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Any</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t"><span class="str">        &#24314;&#31435;WebSocket&#36830;&#25509;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t"><span class="str">            url: WebSocket&#36830;&#25509;&#22320;&#22336;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t"><span class="str">        Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t"><span class="str">            WebSocket&#36830;&#25509;&#23545;&#35937;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t"><span class="str">        Raises:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t"><span class="str">            Exception: &#24403;&#36830;&#25509;&#22833;&#36133;&#26102;&#25243;&#20986;&#24322;&#24120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t"><span class="str">        Example:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t"><span class="str">            >>> ws = Utils.websocket_connection('wss://api.example.com/ws')</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t"><span class="str">            >>> print(ws.getstatus())</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t"><span class="str">            101</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">            <span class="nam">ws</span> <span class="op">=</span> <span class="nam">create_connection</span><span class="op">(</span><span class="nam">url</span><span class="op">,</span> <span class="nam">timeout</span><span class="op">=</span><span class="num">20</span><span class="op">)</span>  <span class="com"># &#21019;&#24314;&#36830;&#25509;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">WebSocket&#36830;&#25509;&#25104;&#21151;&#65292;&#21709;&#24212;&#29366;&#24577;&#30721;&#20026;&#65306;</span><span class="op">{</span><span class="nam">ws</span><span class="op">.</span><span class="nam">getstatus</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f'</span><span class="fst">&#21019;&#24314;WebSocket&#36830;&#25509;&#22833;&#36133;</span><span class="fst">'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f'</span><span class="fst">&#21457;&#29983;&#30340;&#38169;&#35823;&#20026;&#65306;</span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">        <span class="key">return</span> <span class="nam">ws</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">    <span class="key">def</span> <span class="nam">send_websocket</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">ws</span><span class="op">:</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">data</span><span class="op">:</span> <span class="nam">JsonDict</span><span class="op">)</span> <span class="op">-></span> <span class="nam">JsonDict</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t"><span class="str">        &#21457;&#36865;WebSocket&#28040;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t"><span class="str">            ws: WebSocket&#36830;&#25509;&#23545;&#35937;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t"><span class="str">            data: &#35201;&#21457;&#36865;&#30340;JSON&#25968;&#25454;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t"><span class="str">        Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t"><span class="str">            JsonDict: &#26381;&#21153;&#22120;&#21709;&#24212;&#30340;JSON&#25968;&#25454;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t"><span class="str">        Raises:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t"><span class="str">            Exception: &#24403;&#21457;&#36865;&#25110;&#25509;&#25910;&#28040;&#24687;&#22833;&#36133;&#26102;&#25243;&#20986;&#24322;&#24120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t"><span class="str">        Example:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t"><span class="str">            >>> ws = Utils.websocket_connection('wss://api.example.com/ws')</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t"><span class="str">            >>> request_data = {'jsonrpc': '2.0', 'method': 'eth_chainId', 'id': 1}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t"><span class="str">            >>> response = Utils.send_websocket(ws, request_data)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t"><span class="str">            >>> print(response['result'])</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t"><span class="str">            '0x1'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">            <span class="nam">ws</span><span class="op">.</span><span class="nam">send</span><span class="op">(</span><span class="nam">json</span><span class="op">.</span><span class="nam">dumps</span><span class="op">(</span><span class="nam">data</span><span class="op">)</span><span class="op">)</span>  <span class="com"># &#21457;&#36865;&#28040;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f'</span><span class="fst">&#21457;&#36865;&#25968;&#25454;&#25104;&#21151;&#65306;</span><span class="op">{</span><span class="nam">data</span><span class="op">}</span><span class="fst">'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">            <span class="nam">response</span> <span class="op">=</span> <span class="nam">ws</span><span class="op">.</span><span class="nam">recv</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f'</span><span class="fst">&#21457;&#36865;&#25968;&#25454;&#22833;&#36133;&#65292;&#35831;&#27714;&#21442;&#25968;&#20026;&#65306;</span><span class="op">{</span><span class="nam">data</span><span class="op">}</span><span class="fst">'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">exception</span><span class="op">(</span><span class="fst">f'</span><span class="fst">&#21457;&#29983;&#30340;&#38169;&#35823;&#20026;&#65306;</span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">            <span class="key">return</span> <span class="nam">response</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">    <span class="key">def</span> <span class="nam">__api_log</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">method</span><span class="op">,</span> <span class="nam">url</span><span class="op">,</span> <span class="nam">headers</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">params</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">json</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">data</span><span class="op">=</span><span class="key">None</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t"><span class="str">        &#35760;&#24405;&#35831;&#27714;&#26085;&#24535;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t"><span class="str">        :param method: &#35831;&#27714;&#26041;&#24335;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t"><span class="str">        :param url: &#35831;&#27714;&#22320;&#22336;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t"><span class="str">        :param headers: &#35831;&#27714;&#22836;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t"><span class="str">        :param params: &#35831;&#27714;&#21442;&#25968;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t"><span class="str">        :param json: &#35831;&#27714;&#20307;(json)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t"><span class="str">        :param data: &#35831;&#27714;&#20307;(form-data)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t"><span class="str">        :return:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35831;&#27714;&#26041;&#24335;&#65306;</span><span class="op">{</span><span class="nam">method</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35831;&#27714;&#22320;&#22336;&#65306;</span><span class="op">{</span><span class="nam">url</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35831;&#27714;&#22836;&#65306;</span><span class="op">{</span><span class="nam">headers</span> <span class="key">if</span> <span class="nam">headers</span> <span class="key">else</span> <span class="str">'{}'</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span> <span class="com"># &#30830;&#20445;&#25171;&#21360;&#31354;&#23383;&#20856;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35831;&#27714;&#21442;&#25968;&#65306;</span><span class="op">{</span><span class="nam">params</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35831;&#27714;&#20307;(json)&#65306;</span><span class="op">{</span><span class="nam">json</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35831;&#27714;&#20307;(data)&#65306;</span><span class="op">{</span><span class="nam">data</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">    <span class="key">def</span> <span class="nam">handle_yaml</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">file_name</span><span class="op">:</span> <span class="nam">PathLike</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">ConfigDict</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t"><span class="str">        &#35835;&#21462;YAML&#25991;&#20214; - &#20351;&#29992;&#37197;&#32622;&#31649;&#29702;&#22120;&#20248;&#21270;&#24615;&#33021;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t"><span class="str">            file_name: YAML&#25991;&#20214;&#36335;&#24452;&#65292;&#25903;&#25345;&#23383;&#31526;&#20018;&#25110;Path&#23545;&#35937;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t"><span class="str">        Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t"><span class="str">            Optional[ConfigDict]: YAML&#25991;&#20214;&#20869;&#23481;&#30340;&#23383;&#20856;&#65292;&#22914;&#26524;&#25991;&#20214;&#19981;&#23384;&#22312;&#25110;&#35299;&#26512;&#22833;&#36133;&#21017;&#36820;&#22238;None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t"><span class="str">        Raises:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t"><span class="str">            FileNotFoundError: &#24403;&#25991;&#20214;&#19981;&#23384;&#22312;&#26102;&#25243;&#20986;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t"><span class="str">            Exception: &#24403;&#25991;&#20214;&#35299;&#26512;&#22833;&#36133;&#26102;&#25243;&#20986;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t"><span class="str">        Note:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t"><span class="str">            &#27492;&#26041;&#27861;&#20351;&#29992;&#37197;&#32622;&#31649;&#29702;&#22120;&#30340;&#32531;&#23384;&#26426;&#21046;&#65292;&#36991;&#20813;&#37325;&#22797;&#35835;&#21462;&#30456;&#21516;&#25991;&#20214;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t"><span class="str">            &#37197;&#32622;&#31649;&#29702;&#22120;&#20250;&#33258;&#21160;&#26816;&#27979;&#25991;&#20214;&#21464;&#26356;&#24182;&#26356;&#26032;&#32531;&#23384;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t"><span class="str">        Example:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t"><span class="str">            >>> config = Utils.handle_yaml('config/config.yaml')</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t"><span class="str">            >>> if config:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t"><span class="str">            ...     print(config['database']['host'])</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t"><span class="str">            localhost</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">            <span class="com"># &#20351;&#29992;&#37197;&#32622;&#31649;&#29702;&#22120;&#33719;&#21462;&#37197;&#32622;&#65292;&#25903;&#25345;&#32531;&#23384;&#21644;&#28909;&#26356;&#26032;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">            <span class="nam">yaml_data</span> <span class="op">=</span> <span class="nam">cls</span><span class="op">.</span><span class="nam">_config_manager</span><span class="op">.</span><span class="nam">get_config</span><span class="op">(</span><span class="nam">file_name</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">            <span class="key">if</span> <span class="nam">yaml_data</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">                <span class="key">raise</span> <span class="nam">FileNotFoundError</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#26080;&#27861;&#21152;&#36733;&#37197;&#32622;&#25991;&#20214;: </span><span class="op">{</span><span class="nam">file_name</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">            <span class="key">return</span> <span class="nam">yaml_data</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f'</span><span class="fst">YAML&#25991;&#20214;&#35835;&#21462;&#22833;&#36133;&#65292;&#25991;&#20214;&#21517;&#31216;&#65306;</span><span class="op">{</span><span class="nam">file_name</span><span class="op">}</span><span class="fst">, &#38169;&#35823;: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">    <span class="op">@</span><span class="nam">classmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">    <span class="key">def</span> <span class="nam">handle_template</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">source_data</span><span class="op">,</span> <span class="nam">replace_data</span><span class="op">:</span> <span class="nam">dict</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t"><span class="str">        &#26367;&#25442;&#25991;&#26412;&#21464;&#37327;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t"><span class="str">        :param source_data: &#28304;&#25968;&#25454;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t"><span class="str">        :param replace_data: &#38656;&#35201;&#26367;&#25442;&#30340;&#20869;&#23481;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t"><span class="str">        :return:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">        <span class="nam">res</span> <span class="op">=</span> <span class="nam">Template</span><span class="op">(</span><span class="nam">str</span><span class="op">(</span><span class="nam">source_data</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">safe_substitute</span><span class="op">(</span><span class="op">**</span><span class="nam">replace_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">        <span class="key">return</span> <span class="nam">yaml</span><span class="op">.</span><span class="nam">safe_load</span><span class="op">(</span><span class="nam">res</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">    <span class="op">@</span><span class="nam">staticmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">    <span class="key">def</span> <span class="nam">assert_status_and_nodeid</span><span class="op">(</span><span class="nam">response</span><span class="op">:</span> <span class="nam">HttpResponse</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t"><span class="str">        &#26029;&#35328;&#21709;&#24212;&#29366;&#24577;&#30721;&#21644;&#33410;&#28857;ID</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t"><span class="str">        &#39564;&#35777;HTTP&#21709;&#24212;&#30340;&#29366;&#24577;&#30721;&#26159;&#21542;&#20026;200&#65292;&#24182;&#26816;&#26597;&#21709;&#24212;&#22836;&#20013;&#30340;X-Node-Id&#23383;&#27573;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t"><span class="str">            response: HTTP&#21709;&#24212;&#23545;&#35937;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t"><span class="str">        Raises:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t"><span class="str">            AssertionError: &#24403;&#20197;&#19979;&#24773;&#20917;&#21457;&#29983;&#26102;&#25243;&#20986;&#65306;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t"><span class="str">                - &#21709;&#24212;&#29366;&#24577;&#30721;&#19981;&#26159;200</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t"><span class="str">                - &#21709;&#24212;&#22836;&#20013;&#32570;&#23569;'X-Node-Id'&#23383;&#27573;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t"><span class="str">                - 'X-Node-Id'&#23383;&#27573;&#20540;&#20026;&#31354;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t"><span class="str">                - 'X-Node-Id'&#23383;&#27573;&#20540;&#19981;&#21253;&#21547;'0x'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t"><span class="str">        Example:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t"><span class="str">            >>> response = requests.get('https://api.example.com/rpc')</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t"><span class="str">            >>> Utils.assert_status_and_nodeid(response)  # &#22914;&#26524;&#39564;&#35777;&#36890;&#36807;&#21017;&#26080;&#36820;&#22238;&#20540;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">            <span class="key">assert</span> <span class="nam">response</span><span class="op">.</span><span class="nam">status_code</span> <span class="op">==</span> <span class="num">200</span><span class="op">,</span> <span class="fst">f"</span><span class="fst">&#21709;&#24212;&#29366;&#24577;&#30721;&#38169;&#35823;&#65292;&#39044;&#26399; 200&#65292;&#23454;&#38469; </span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">status_code</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">            <span class="com"># &#30830;&#20445; X-Node-Id &#23384;&#22312;&#19988;&#19981;&#20026;&#31354;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t">            <span class="key">assert</span> <span class="str">'X-Node-Id'</span> <span class="key">in</span> <span class="nam">response</span><span class="op">.</span><span class="nam">headers</span><span class="op">,</span> <span class="str">"&#21709;&#24212;&#22836;&#32570;&#23569; 'X-Node-Id'"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">            <span class="key">assert</span> <span class="nam">response</span><span class="op">.</span><span class="nam">headers</span><span class="op">[</span><span class="str">'X-Node-Id'</span><span class="op">]</span><span class="op">,</span> <span class="str">"'X-Node-Id' &#21709;&#24212;&#22836;&#20540;&#20026;&#31354;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">            <span class="key">assert</span> <span class="str">'0x'</span> <span class="key">in</span> <span class="nam">response</span><span class="op">.</span><span class="nam">headers</span><span class="op">[</span><span class="str">'X-Node-Id'</span><span class="op">]</span><span class="op">,</span> <span class="fst">f"</span><span class="fst">'X-Node-Id' (</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">headers</span><span class="op">[</span><span class="str">'X-Node-Id'</span><span class="op">]</span><span class="op">}</span><span class="fst">) &#19981;&#21253;&#21547; '0x'</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t329" href="#t329">329</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#26029;&#35328;&#25104;&#21151;: &#29366;&#24577;&#30721;=</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">status_code</span><span class="op">}</span><span class="fst">, X-Node-Id=</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">headers</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'X-Node-Id'</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t330" href="#t330">330</a></span><span class="t">        <span class="key">except</span> <span class="nam">AssertionError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t331" href="#t331">331</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">eq&#26029;&#35328;&#22833;&#36133;&#65292;&#39044;&#26399;&#32467;&#26524;&#65306;200&#65292;&#23454;&#38469;&#32467;&#26524;&#65306;</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">status_code</span> <span class="key">if</span> <span class="nam">response</span> <span class="key">else</span> <span class="str">'N/A'</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t332" href="#t332">332</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#29992;&#20363;&#22833;&#36133;&#65281;&#26029;&#35328;&#20449;&#24687;: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t333" href="#t333">333</a></span><span class="t">            <span class="com"># &#21487;&#20197;&#36873;&#25321;&#22312;&#36825;&#37324;&#35760;&#24405;&#26356;&#35814;&#32454;&#30340;&#21709;&#24212;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t334" href="#t334">334</a></span><span class="t">            <span class="key">if</span> <span class="nam">response</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t335" href="#t335">335</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#22833;&#36133;&#21709;&#24212;&#35814;&#24773;: Status=</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">status_code</span><span class="op">}</span><span class="fst">, Headers=</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">headers</span><span class="op">}</span><span class="fst">, Body=</span><span class="op">{</span><span class="nam">response</span><span class="op">.</span><span class="nam">text</span><span class="op">[</span><span class="op">:</span><span class="num">500</span><span class="op">]</span><span class="op">}</span><span class="fst">...</span><span class="fst">"</span><span class="op">)</span> <span class="com"># &#35760;&#24405;&#37096;&#20998;&#21709;&#24212;&#20307;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t336" href="#t336">336</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span> <span class="com"># &#23558;&#26029;&#35328;&#38169;&#35823;&#21521;&#19978;&#25243;&#20986;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t337" href="#t337">337</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t338" href="#t338">338</a></span><span class="t">    <span class="op">@</span><span class="nam">staticmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t339" href="#t339">339</a></span><span class="t">    <span class="key">def</span> <span class="nam">assert_contains</span><span class="op">(</span><span class="nam">content</span><span class="op">,</span> <span class="nam">expected</span><span class="op">=</span><span class="str">'0x'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t340" href="#t340">340</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t341" href="#t341">341</a></span><span class="t"><span class="str">        &#26029;&#35328;&#21253;&#21547;&#65292;&#40664;&#35748;&#21442;&#25968;0x</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t342" href="#t342">342</a></span><span class="t"><span class="str">        :param content: &#25991;&#26412;&#20869;&#23481;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t343" href="#t343">343</a></span><span class="t"><span class="str">        :param expected: &#30446;&#26631;&#25991;&#26412;&#65288;&#23383;&#31526;&#20018;&#25110;&#23383;&#31526;&#20018;&#21015;&#34920;&#65289;&#25110;&#39044;&#26399;&#32467;&#26524;&#25968;&#32452;&#65288;&#21253;&#21547;&#23383;&#20856;&#65289;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t344" href="#t344">344</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t345" href="#t345">345</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t346" href="#t346">346</a></span><span class="t">            <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">expected</span><span class="op">,</span> <span class="nam">list</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t347" href="#t347">347</a></span><span class="t">                <span class="com"># &#26816;&#26597;&#21015;&#34920;&#20013;&#30340;&#20803;&#32032;&#31867;&#22411;&#65292;&#20197;&#21306;&#20998;&#8220;&#25110;&#8221;&#36923;&#36753;&#21644;&#25968;&#32452;&#27604;&#36739;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t348" href="#t348">348</a></span><span class="t">                <span class="key">if</span> <span class="nam">all</span><span class="op">(</span><span class="nam">isinstance</span><span class="op">(</span><span class="nam">item</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span> <span class="key">for</span> <span class="nam">item</span> <span class="key">in</span> <span class="nam">expected</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t349" href="#t349">349</a></span><span class="t">                    <span class="com"># &#22914;&#26524;expected&#26159;&#23383;&#31526;&#20018;&#21015;&#34920;&#65292;&#25191;&#34892;&#8220;&#25110;&#8221;&#36923;&#36753;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t350" href="#t350">350</a></span><span class="t">                    <span class="nam">content_str</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t351" href="#t351">351</a></span><span class="t">                    <span class="key">assert</span> <span class="nam">any</span><span class="op">(</span><span class="nam">item</span> <span class="key">in</span> <span class="nam">content_str</span> <span class="key">for</span> <span class="nam">item</span> <span class="key">in</span> <span class="nam">expected</span><span class="op">)</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t352" href="#t352">352</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#23383;&#31526;&#20018;&#21253;&#21547;&#26029;&#35328;&#22833;&#36133; - &#26410;&#25214;&#21040;&#26399;&#26395;&#30340;&#20869;&#23481;: </span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t353" href="#t353">353</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#20869;&#23481;: </span><span class="op">{</span><span class="nam">content_str</span><span class="op">[</span><span class="op">:</span><span class="num">200</span><span class="op">]</span><span class="op">}</span><span class="op">{</span><span class="str">'...'</span> <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">content_str</span><span class="op">)</span> <span class="op">></span> <span class="num">200</span> <span class="key">else</span> <span class="str">''</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t354" href="#t354">354</a></span><span class="t">                <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t355" href="#t355">355</a></span><span class="t">                    <span class="com"># &#22914;&#26524;expected&#26159;&#21253;&#21547;&#23383;&#20856;&#30340;&#21015;&#34920;&#65292;&#25191;&#34892;&#25968;&#32452;&#32467;&#26500;&#21644;&#20869;&#23481;&#27604;&#36739;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t356" href="#t356">356</a></span><span class="t">                    <span class="key">assert</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">content</span><span class="op">,</span> <span class="nam">list</span><span class="op">)</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t357" href="#t357">357</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#25968;&#32452;&#31867;&#22411;&#26029;&#35328;&#22833;&#36133; - &#23454;&#38469;&#20869;&#23481;&#19981;&#26159;&#21015;&#34920;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t358" href="#t358">358</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#26399;&#26395;&#31867;&#22411;: list, &#23454;&#38469;&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t359" href="#t359">359</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#20869;&#23481;: </span><span class="op">{</span><span class="nam">content</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t360" href="#t360">360</a></span><span class="t">                    <span class="key">assert</span> <span class="nam">len</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span> <span class="op">==</span> <span class="nam">len</span><span class="op">(</span><span class="nam">expected</span><span class="op">)</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t361" href="#t361">361</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#25968;&#32452;&#38271;&#24230;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t362" href="#t362">362</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#26399;&#26395;&#38271;&#24230;: </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">expected</span><span class="op">)</span><span class="op">}</span><span class="fst">, &#23454;&#38469;&#38271;&#24230;: </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t363" href="#t363">363</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#26399;&#26395;&#20869;&#23481;: </span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t364" href="#t364">364</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#20869;&#23481;: </span><span class="op">{</span><span class="nam">content</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t365" href="#t365">365</a></span><span class="t">                    <span class="key">for</span> <span class="nam">exp_item</span><span class="op">,</span> <span class="nam">actual_item</span> <span class="key">in</span> <span class="nam">zip</span><span class="op">(</span><span class="nam">expected</span><span class="op">,</span> <span class="nam">content</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t366" href="#t366">366</a></span><span class="t">                        <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">exp_item</span><span class="op">,</span> <span class="nam">dict</span><span class="op">)</span> <span class="key">and</span> <span class="str">'result'</span> <span class="key">in</span> <span class="nam">exp_item</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t367" href="#t367">367</a></span><span class="t">                            <span class="com"># &#22788;&#29702; result &#20013;&#21253;&#21547;&#36890;&#37197;&#31526;&#30340;&#24773;&#20917;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t368" href="#t368">368</a></span><span class="t">                            <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">exp_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span> <span class="key">and</span> <span class="nam">exp_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">.</span><span class="nam">endswith</span><span class="op">(</span><span class="str">'*'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t369" href="#t369">369</a></span><span class="t">                                <span class="nam">prefix</span> <span class="op">=</span> <span class="nam">exp_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">[</span><span class="op">:</span><span class="op">-</span><span class="num">1</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t370" href="#t370">370</a></span><span class="t">                                <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">actual_item</span><span class="op">,</span> <span class="nam">dict</span><span class="op">)</span> <span class="key">and</span> <span class="str">'result'</span> <span class="key">in</span> <span class="nam">actual_item</span> <span class="key">and</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">actual_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t371" href="#t371">371</a></span><span class="t">                                    <span class="key">assert</span> <span class="nam">actual_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">.</span><span class="nam">startswith</span><span class="op">(</span><span class="nam">prefix</span><span class="op">)</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t372" href="#t372">372</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#21069;&#32512;&#21305;&#37197;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t373" href="#t373">373</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#26399;&#26395;&#21069;&#32512;: </span><span class="op">{</span><span class="nam">prefix</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t374" href="#t374">374</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#20540;: </span><span class="op">{</span><span class="nam">actual_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t375" href="#t375">375</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#23436;&#25972;&#26399;&#26395;&#39033;: </span><span class="op">{</span><span class="nam">exp_item</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t376" href="#t376">376</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#23436;&#25972;&#23454;&#38469;&#39033;: </span><span class="op">{</span><span class="nam">actual_item</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t377" href="#t377">377</a></span><span class="t">                                <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t378" href="#t378">378</a></span><span class="t">                                    <span class="com"># &#22914;&#26524; result &#26159;&#23383;&#20856;&#65288;&#27604;&#22914;&#21306;&#22359;&#20449;&#24687;&#65289;&#65292;&#21017;&#36339;&#36807;&#21069;&#32512;&#26816;&#26597;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t379" href="#t379">379</a></span><span class="t">                                    <span class="key">assert</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">actual_item</span><span class="op">,</span> <span class="nam">dict</span><span class="op">)</span> <span class="key">and</span> <span class="str">'result'</span> <span class="key">in</span> <span class="nam">actual_item</span> <span class="key">and</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">actual_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">,</span> <span class="nam">dict</span><span class="op">)</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t380" href="#t380">380</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#23383;&#20856;&#32467;&#26500;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t381" href="#t381">381</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#26399;&#26395;: &#21253;&#21547; 'result' &#23383;&#27573;&#30340;&#23383;&#20856;&#31867;&#22411;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t382" href="#t382">382</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#20540;: </span><span class="op">{</span><span class="nam">actual_item</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t383" href="#t383">383</a></span><span class="t">                                        <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">actual_item</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t384" href="#t384">384</a></span><span class="t">                            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t385" href="#t385">385</a></span><span class="t">                                <span class="key">assert</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">actual_item</span><span class="op">,</span> <span class="nam">dict</span><span class="op">)</span> <span class="key">and</span> <span class="str">'result'</span> <span class="key">in</span> <span class="nam">actual_item</span> <span class="key">and</span> <span class="nam">exp_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span> <span class="op">==</span> <span class="nam">actual_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t386" href="#t386">386</a></span><span class="t">                                    <span class="fst">f"</span><span class="fst">&#32467;&#26524;&#20540;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t387" href="#t387">387</a></span><span class="t">                                    <span class="fst">f"</span><span class="fst">&#26399;&#26395;&#32467;&#26524;: </span><span class="op">{</span><span class="nam">exp_item</span><span class="op">[</span><span class="str">'result'</span><span class="op">]</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t388" href="#t388">388</a></span><span class="t">                                    <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#32467;&#26524;: </span><span class="op">{</span><span class="nam">actual_item</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'result'</span><span class="op">,</span> <span class="str">'MISSING_RESULT_FIELD'</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t389" href="#t389">389</a></span><span class="t">                                    <span class="fst">f"</span><span class="fst">&#23436;&#25972;&#26399;&#26395;&#39033;: </span><span class="op">{</span><span class="nam">exp_item</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t390" href="#t390">390</a></span><span class="t">                                    <span class="fst">f"</span><span class="fst">&#23436;&#25972;&#23454;&#38469;&#39033;: </span><span class="op">{</span><span class="nam">actual_item</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t391" href="#t391">391</a></span><span class="t">                        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t392" href="#t392">392</a></span><span class="t">                            <span class="com"># &#23545;&#20110;&#38750;&#23383;&#20856;&#20803;&#32032;&#65292;&#36827;&#34892;&#30452;&#25509;&#30456;&#31561;&#27604;&#36739;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t393" href="#t393">393</a></span><span class="t">                            <span class="key">assert</span> <span class="nam">exp_item</span> <span class="op">==</span> <span class="nam">actual_item</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t394" href="#t394">394</a></span><span class="t">                                <span class="fst">f"</span><span class="fst">&#25968;&#32452;&#20803;&#32032;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t395" href="#t395">395</a></span><span class="t">                                <span class="fst">f"</span><span class="fst">&#26399;&#26395;: </span><span class="op">{</span><span class="nam">exp_item</span><span class="op">}</span><span class="fst"> (&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">exp_item</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">)\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t396" href="#t396">396</a></span><span class="t">                                <span class="fst">f"</span><span class="fst">&#23454;&#38469;: </span><span class="op">{</span><span class="nam">actual_item</span><span class="op">}</span><span class="fst"> (&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">actual_item</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t397" href="#t397">397</a></span><span class="t">            <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">expected</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t398" href="#t398">398</a></span><span class="t">                <span class="com"># &#21407;&#26377;&#30340;&#23383;&#31526;&#20018;&#21253;&#21547;&#36923;&#36753;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t399" href="#t399">399</a></span><span class="t">                <span class="nam">content_str</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t400" href="#t400">400</a></span><span class="t">                <span class="key">assert</span> <span class="nam">expected</span> <span class="key">in</span> <span class="nam">content_str</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t401" href="#t401">401</a></span><span class="t">                    <span class="fst">f"</span><span class="fst">&#23383;&#31526;&#20018;&#21253;&#21547;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t402" href="#t402">402</a></span><span class="t">                    <span class="fst">f"</span><span class="fst">&#26399;&#26395;&#21253;&#21547;: '</span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">'\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t403" href="#t403">403</a></span><span class="t">                    <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#20869;&#23481;: </span><span class="op">{</span><span class="nam">content_str</span><span class="op">[</span><span class="op">:</span><span class="num">300</span><span class="op">]</span><span class="op">}</span><span class="op">{</span><span class="str">'...'</span> <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">content_str</span><span class="op">)</span> <span class="op">></span> <span class="num">300</span> <span class="key">else</span> <span class="str">''</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t404" href="#t404">404</a></span><span class="t">                    <span class="fst">f"</span><span class="fst">&#20869;&#23481;&#38271;&#24230;: </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">content_str</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t405" href="#t405">405</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t406" href="#t406">406</a></span><span class="t">                <span class="key">raise</span> <span class="nam">TypeError</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#21442;&#25968;&#31867;&#22411;&#38169;&#35823; - expected &#21442;&#25968;&#24517;&#39035;&#26159;&#23383;&#31526;&#20018;&#12289;&#23383;&#31526;&#20018;&#21015;&#34920;&#25110;&#21253;&#21547;&#23383;&#20856;&#30340;&#21015;&#34920;\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t407" href="#t407">407</a></span><span class="t">                              <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">expected</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t408" href="#t408">408</a></span><span class="t">                              <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#20540;: </span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t409" href="#t409">409</a></span><span class="t">        <span class="key">except</span> <span class="nam">AssertionError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t410" href="#t410">410</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">assert_contains &#26029;&#35328;&#22833;&#36133;: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t411" href="#t411">411</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"&#29992;&#20363;&#22833;&#36133;&#65281;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t412" href="#t412">412</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t413" href="#t413">413</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t414" href="#t414">414</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">assert_contains &#25191;&#34892;&#24322;&#24120;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t415" href="#t415">415</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#26029;&#35328;&#21442;&#25968; - expected: </span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">, content: </span><span class="op">{</span><span class="nam">content</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t416" href="#t416">416</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t417" href="#t417">417</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t418" href="#t418">418</a></span><span class="t">    <span class="op">@</span><span class="nam">staticmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t419" href="#t419">419</a></span><span class="t">    <span class="key">def</span> <span class="nam">assert_id_and_version</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t420" href="#t420">420</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t421" href="#t421">421</a></span><span class="t"><span class="str">        &#26029;&#35328;&#21709;&#24212;id&#21644;version</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t422" href="#t422">422</a></span><span class="t"><span class="str">        :param content: &#25991;&#26412;&#20869;&#23481;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t423" href="#t423">423</a></span><span class="t"><span class="str">        :return:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t424" href="#t424">424</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t425" href="#t425">425</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t426" href="#t426">426</a></span><span class="t">            <span class="com"># &#26816;&#26597; content &#26159;&#21542;&#20026;&#23383;&#20856;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t427" href="#t427">427</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">content</span><span class="op">,</span> <span class="nam">dict</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t428" href="#t428">428</a></span><span class="t">                <span class="key">raise</span> <span class="nam">AssertionError</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#21709;&#24212;&#20869;&#23481;&#31867;&#22411;&#38169;&#35823; - &#26399;&#26395;&#23383;&#20856;&#31867;&#22411;&#65292;&#23454;&#38469;&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t429" href="#t429">429</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t430" href="#t430">430</a></span><span class="t">            <span class="com"># &#26816;&#26597;&#24517;&#38656;&#23383;&#27573;&#26159;&#21542;&#23384;&#22312;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t431" href="#t431">431</a></span><span class="t">            <span class="nam">missing_fields</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t432" href="#t432">432</a></span><span class="t">            <span class="key">if</span> <span class="str">'id'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">content</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t433" href="#t433">433</a></span><span class="t">                <span class="nam">missing_fields</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">'id'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t434" href="#t434">434</a></span><span class="t">            <span class="key">if</span> <span class="str">'jsonrpc'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">content</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t435" href="#t435">435</a></span><span class="t">                <span class="nam">missing_fields</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">'jsonrpc'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t436" href="#t436">436</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t437" href="#t437">437</a></span><span class="t">            <span class="key">if</span> <span class="nam">missing_fields</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t438" href="#t438">438</a></span><span class="t">                <span class="key">raise</span> <span class="nam">AssertionError</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#21709;&#24212;&#32570;&#23569;&#24517;&#38656;&#23383;&#27573;: </span><span class="op">{</span><span class="nam">missing_fields</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t439" href="#t439">439</a></span><span class="t">                                   <span class="fst">f"</span><span class="fst">&#23454;&#38469;&#23383;&#27573;: </span><span class="op">{</span><span class="nam">list</span><span class="op">(</span><span class="nam">content</span><span class="op">.</span><span class="nam">keys</span><span class="op">(</span><span class="op">)</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t440" href="#t440">440</a></span><span class="t">                                   <span class="fst">f"</span><span class="fst">&#23436;&#25972;&#21709;&#24212;: </span><span class="op">{</span><span class="nam">content</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t441" href="#t441">441</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t442" href="#t442">442</a></span><span class="t">            <span class="com"># &#26816;&#26597;&#23383;&#27573;&#20540;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t443" href="#t443">443</a></span><span class="t">            <span class="nam">id_value</span> <span class="op">=</span> <span class="nam">content</span><span class="op">[</span><span class="str">'id'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t444" href="#t444">444</a></span><span class="t">            <span class="nam">jsonrpc_value</span> <span class="op">=</span> <span class="nam">content</span><span class="op">[</span><span class="str">'jsonrpc'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t445" href="#t445">445</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t446" href="#t446">446</a></span><span class="t">            <span class="nam">id_valid</span> <span class="op">=</span> <span class="nam">id_value</span> <span class="op">==</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t447" href="#t447">447</a></span><span class="t">            <span class="nam">jsonrpc_valid</span> <span class="op">=</span> <span class="nam">jsonrpc_value</span> <span class="op">==</span> <span class="str">'2.0'</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t448" href="#t448">448</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t449" href="#t449">449</a></span><span class="t">            <span class="key">assert</span> <span class="nam">id_valid</span> <span class="key">and</span> <span class="nam">jsonrpc_valid</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t450" href="#t450">450</a></span><span class="t">                <span class="fst">f"</span><span class="fst">JSON-RPC &#23383;&#27573;&#20540;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t451" href="#t451">451</a></span><span class="t">                <span class="fst">f"</span><span class="fst">ID &#26029;&#35328;: &#26399;&#26395;=1, &#23454;&#38469;=</span><span class="op">{</span><span class="nam">id_value</span><span class="op">}</span><span class="fst"> (&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">id_value</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">) </span><span class="op">{</span><span class="str">'&#10003;'</span> <span class="key">if</span> <span class="nam">id_valid</span> <span class="key">else</span> <span class="str">'&#10007;'</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t452" href="#t452">452</a></span><span class="t">                <span class="fst">f"</span><span class="fst">JSONRPC &#26029;&#35328;: &#26399;&#26395;='2.0', &#23454;&#38469;='</span><span class="op">{</span><span class="nam">jsonrpc_value</span><span class="op">}</span><span class="fst">' (&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">jsonrpc_value</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">) </span><span class="op">{</span><span class="str">'&#10003;'</span> <span class="key">if</span> <span class="nam">jsonrpc_valid</span> <span class="key">else</span> <span class="str">'&#10007;'</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t453" href="#t453">453</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#23436;&#25972;&#21709;&#24212;: </span><span class="op">{</span><span class="nam">content</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t454" href="#t454">454</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t455" href="#t455">455</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">JSON-RPC &#23383;&#27573;&#39564;&#35777;&#25104;&#21151;: id=</span><span class="op">{</span><span class="nam">id_value</span><span class="op">}</span><span class="fst">, jsonrpc=</span><span class="op">{</span><span class="nam">jsonrpc_value</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t456" href="#t456">456</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t457" href="#t457">457</a></span><span class="t">        <span class="key">except</span> <span class="nam">AssertionError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t458" href="#t458">458</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">assert_id_and_version &#26029;&#35328;&#22833;&#36133;: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t459" href="#t459">459</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"&#29992;&#20363;&#22833;&#36133;&#65281;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t460" href="#t460">460</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t461" href="#t461">461</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t462" href="#t462">462</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">assert_id_and_version &#25191;&#34892;&#24322;&#24120;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t463" href="#t463">463</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#26029;&#35328;&#21442;&#25968;&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">, &#20869;&#23481;: </span><span class="op">{</span><span class="nam">content</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t464" href="#t464">464</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t465" href="#t465">465</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t466" href="#t466">466</a></span><span class="t">    <span class="op">@</span><span class="nam">staticmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t467" href="#t467">467</a></span><span class="t">    <span class="key">def</span> <span class="nam">assert_not_contains</span><span class="op">(</span><span class="nam">content</span><span class="op">,</span> <span class="nam">expected</span><span class="op">=</span><span class="str">'error'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t468" href="#t468">468</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t469" href="#t469">469</a></span><span class="t"><span class="str">        &#26029;&#35328;&#21709;&#24212;&#25991;&#26412;&#19981;&#21253;&#21547;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t470" href="#t470">470</a></span><span class="t"><span class="str">        :param content: &#25991;&#26412;&#20869;&#23481;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t471" href="#t471">471</a></span><span class="t"><span class="str">        :param expected: &#30446;&#26631;&#25991;&#26412;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t472" href="#t472">472</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t473" href="#t473">473</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t474" href="#t474">474</a></span><span class="t">            <span class="nam">content_str</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t475" href="#t475">475</a></span><span class="t">            <span class="nam">contains_unexpected</span> <span class="op">=</span> <span class="nam">expected</span> <span class="key">in</span> <span class="nam">content_str</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t476" href="#t476">476</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t477" href="#t477">477</a></span><span class="t">            <span class="key">assert</span> <span class="key">not</span> <span class="nam">contains_unexpected</span><span class="op">,</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t478" href="#t478">478</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#19981;&#24212;&#21253;&#21547;&#20869;&#23481;&#26029;&#35328;&#22833;&#36133;\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t479" href="#t479">479</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#19981;&#24212;&#21253;&#21547;: '</span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">'\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t480" href="#t480">480</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#20294;&#22312;&#20869;&#23481;&#20013;&#21457;&#29616;&#20102;: </span><span class="op">{</span><span class="str">'&#26159;'</span> <span class="key">if</span> <span class="nam">contains_unexpected</span> <span class="key">else</span> <span class="str">'&#21542;'</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t481" href="#t481">481</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#20869;&#23481;&#25688;&#35201;: </span><span class="op">{</span><span class="nam">content_str</span><span class="op">[</span><span class="op">:</span><span class="num">200</span><span class="op">]</span><span class="op">}</span><span class="op">{</span><span class="str">'...'</span> <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">content_str</span><span class="op">)</span> <span class="op">></span> <span class="num">200</span> <span class="key">else</span> <span class="str">''</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t482" href="#t482">482</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#20869;&#23481;&#38271;&#24230;: </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">content_str</span><span class="op">)</span><span class="op">}</span><span class="fst">\n</span><span class="fst">"</span> <span class="xx">\</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t483" href="#t483">483</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#20869;&#23481;&#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t484" href="#t484">484</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t485" href="#t485">485</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#19981;&#21253;&#21547;&#26029;&#35328;&#25104;&#21151;: &#20869;&#23481;&#20013;&#26410;&#21457;&#29616; '</span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">'</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t486" href="#t486">486</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t487" href="#t487">487</a></span><span class="t">        <span class="key">except</span> <span class="nam">AssertionError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t488" href="#t488">488</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">assert_not_contains &#26029;&#35328;&#22833;&#36133;: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t489" href="#t489">489</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"&#29992;&#20363;&#22833;&#36133;&#65281;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t490" href="#t490">490</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t491" href="#t491">491</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t492" href="#t492">492</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">assert_not_contains &#25191;&#34892;&#24322;&#24120;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t493" href="#t493">493</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#26029;&#35328;&#21442;&#25968; - expected: '</span><span class="op">{</span><span class="nam">expected</span><span class="op">}</span><span class="fst">', content &#31867;&#22411;: </span><span class="op">{</span><span class="nam">type</span><span class="op">(</span><span class="nam">content</span><span class="op">)</span><span class="op">.</span><span class="nam">__name__</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t494" href="#t494">494</a></span><span class="t">            <span class="key">raise</span> <span class="nam">e</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t495" href="#t495">495</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t496" href="#t496">496</a></span><span class="t">    <span class="op">@</span><span class="nam">staticmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t497" href="#t497">497</a></span><span class="t">    <span class="key">def</span> <span class="nam">tx_decoder</span><span class="op">(</span><span class="nam">encoded_tx</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t498" href="#t498">498</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t499" href="#t499">499</a></span><span class="t"><span class="str">        &#35299;&#30721;&#20132;&#26131;&#24182;&#35745;&#31639;&#21704;&#24076;&#20540;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t500" href="#t500">500</a></span><span class="t"><span class="str">        :param encoded_tx: base64&#32534;&#30721;&#30340;&#20132;&#26131;&#25968;&#25454;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t501" href="#t501">501</a></span><span class="t"><span class="str">        :return: &#20132;&#26131;&#21704;&#24076;&#20540;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t502" href="#t502">502</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t503" href="#t503">503</a></span><span class="t">        <span class="nam">decoded_tx</span> <span class="op">=</span> <span class="nam">base64</span><span class="op">.</span><span class="nam">b64decode</span><span class="op">(</span><span class="nam">encoded_tx</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t504" href="#t504">504</a></span><span class="t">        <span class="nam">tx_hash</span> <span class="op">=</span> <span class="nam">hashlib</span><span class="op">.</span><span class="nam">sha256</span><span class="op">(</span><span class="nam">decoded_tx</span><span class="op">)</span><span class="op">.</span><span class="nam">hexdigest</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t505" href="#t505">505</a></span><span class="t">        <span class="key">return</span> <span class="nam">tx_hash</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t506" href="#t506">506</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t507" href="#t507">507</a></span><span class="t">    <span class="op">@</span><span class="nam">staticmethod</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t508" href="#t508">508</a></span><span class="t">    <span class="key">def</span> <span class="nam">read_yaml</span><span class="op">(</span><span class="nam">file_path</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t509" href="#t509">509</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t510" href="#t510">510</a></span><span class="t"><span class="str">        &#35835;&#21462;yaml&#25991;&#20214;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t511" href="#t511">511</a></span><span class="t"><span class="str">        :param file_path: yaml&#25991;&#20214;&#36335;&#24452;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t512" href="#t512">512</a></span><span class="t"><span class="str">        :return:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t513" href="#t513">513</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t514" href="#t514">514</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t515" href="#t515">515</a></span><span class="t">            <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">file_path</span><span class="op">,</span> <span class="str">'r'</span><span class="op">,</span> <span class="nam">encoding</span><span class="op">=</span><span class="str">'utf-8'</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t516" href="#t516">516</a></span><span class="t">                <span class="key">return</span> <span class="nam">yaml</span><span class="op">.</span><span class="nam">safe_load</span><span class="op">(</span><span class="nam">f</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t517" href="#t517">517</a></span><span class="t">        <span class="key">except</span> <span class="nam">FileNotFoundError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t518" href="#t518">518</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">YAML &#25991;&#20214;&#26410;&#25214;&#21040;: </span><span class="op">{</span><span class="nam">file_path</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t519" href="#t519">519</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t520" href="#t520">520</a></span><span class="t">        <span class="key">except</span> <span class="nam">yaml</span><span class="op">.</span><span class="nam">YAMLError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t521" href="#t521">521</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35299;&#26512; YAML &#25991;&#20214;&#26102;&#20986;&#38169;: </span><span class="op">{</span><span class="nam">file_path</span><span class="op">}</span><span class="fst">, Error: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t522" href="#t522">522</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t523" href="#t523">523</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t524" href="#t524">524</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35835;&#21462; YAML &#25991;&#20214;&#26102;&#21457;&#29983;&#26410;&#30693;&#38169;&#35823;: </span><span class="op">{</span><span class="nam">file_path</span><span class="op">}</span><span class="fst">, Error: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t525" href="#t525">525</a></span><span class="t">            <span class="key">return</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_bfc844598bd03e84_types_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_bfc844598bd03e84_utils_refactored_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 14:59 +0800
        </p>
    </div>
</footer>
</body>
</html>
