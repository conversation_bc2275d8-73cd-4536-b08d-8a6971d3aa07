<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">35.85%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 14:59 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8___init___py.html">apis/__init__.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_aptos_api_py.html#t7">apis/aptos_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_aptos_api_py.html#t7"><data value='AptosApi'>AptosApi</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_aptos_api_py.html">apis/aptos_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_aptos_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_base_api_py.html#t14">apis/base_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_base_api_py.html#t14"><data value='BaseApi'>BaseApi</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_base_api_py.html">apis/base_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_base_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_beacon_api_py.html#t7">apis/beacon_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_beacon_api_py.html#t7"><data value='BeaconApi'>BeaconApi</data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_beacon_api_py.html">apis/beacon_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_beacon_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="54 54">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_cosmos_api_py.html#t7">apis/cosmos_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_cosmos_api_py.html#t7"><data value='CosmosApi'>CosmosApi</data></a></td>
                <td>270</td>
                <td>270</td>
                <td>0</td>
                <td class="right" data-ratio="0 270">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_cosmos_api_py.html">apis/cosmos_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_cosmos_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>188</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="188 188">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_jsonrpc_api_py.html#t15">apis/jsonrpc_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_jsonrpc_api_py.html#t15"><data value='JsonrpcApi'>JsonrpcApi</data></a></td>
                <td>103</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="53 103">51.46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_jsonrpc_api_py.html">apis/jsonrpc_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_jsonrpc_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>149</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="149 149">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_linea_api_py.html#t6">apis/linea_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_linea_api_py.html#t6"><data value='LineaApi'>LineaApi</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_linea_api_py.html">apis/linea_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_linea_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_near_api_py.html#t7">apis/near_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_near_api_py.html#t7"><data value='NearApi'>NearApi</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_near_api_py.html">apis/near_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_near_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_solana_api_py.html#t6">apis/solana_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_solana_api_py.html#t6"><data value='SolanaApi'>SolanaApi</data></a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_solana_api_py.html">apis/solana_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_solana_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_starknet_api_py.html#t6">apis/starknet_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_starknet_api_py.html#t6"><data value='StarknetApi'>StarknetApi</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_starknet_api_py.html">apis/starknet_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_starknet_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="47 47">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_sui_api_py.html#t7">apis/sui_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_sui_api_py.html#t7"><data value='SuiApi'>SuiApi</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_sui_api_py.html">apis/sui_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_sui_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="64 64">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_ton_api_py.html#t6">apis/ton_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_ton_api_py.html#t6"><data value='TonApi'>TonApi</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b48200c0765dce8_ton_api_py.html">apis/ton_api.py</a></td>
                <td class="name left"><a href="z_1b48200c0765dce8_ton_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84___init___py.html">common/__init__.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_assertion_utils_py.html#t10">common/assertion_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_assertion_utils_py.html#t10"><data value='AssertionUtils'>AssertionUtils</data></a></td>
                <td>89</td>
                <td>89</td>
                <td>2</td>
                <td class="right" data-ratio="0 89">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_assertion_utils_py.html">common/assertion_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_assertion_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_config_handler_py.html">common/config_handler.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_config_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="21 78">26.92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_config_manager_py.html#t22">common/config_manager.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_config_manager_py.html#t22"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>64</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="34 64">53.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_config_manager_py.html">common/config_manager.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_crypto_utils_py.html#t11">common/crypto_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_crypto_utils_py.html#t11"><data value='CryptoUtils'>CryptoUtils</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_crypto_utils_py.html">common/crypto_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_crypto_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_assertion_utils_py.html#t13">common/enhanced_assertion_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_assertion_utils_py.html#t13"><data value='EnhancedAssertionError'>EnhancedAssertionError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_assertion_utils_py.html#t21">common/enhanced_assertion_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_assertion_utils_py.html#t21"><data value='EnhancedAssertionUtils'>EnhancedAssertionUtils</data></a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_assertion_utils_py.html">common/enhanced_assertion_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_assertion_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_config_manager_py.html#t17">common/enhanced_config_manager.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_config_manager_py.html#t17"><data value='EnhancedConfigManager'>EnhancedConfigManager</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_config_manager_py.html">common/enhanced_config_manager.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_enhanced_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_error_config_loader_py.html#t14">common/error_config_loader.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_error_config_loader_py.html#t14"><data value='ErrorReportingConfigLoader'>ErrorReportingConfigLoader</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_error_config_loader_py.html">common/error_config_loader.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_error_config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_error_reporter_py.html#t16">common/error_reporter.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_error_reporter_py.html#t16"><data value='ErrorReporter'>ErrorReporter</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_error_reporter_py.html">common/error_reporter.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_error_reporter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_handle_path_py.html">common/handle_path.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_handle_path_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_ip_mode_handler_py.html#t8">common/ip_mode_handler.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_ip_mode_handler_py.html#t8"><data value='IPModeHandler'>IPModeHandler</data></a></td>
                <td>47</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="4 47">8.51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_ip_mode_handler_py.html">common/ip_mode_handler.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_ip_mode_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_network_client_py.html#t16">common/network_client.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_network_client_py.html#t16"><data value='NetworkClient'>NetworkClient</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_network_client_py.html">common/network_client.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_network_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_template_utils_py.html#t12">common/template_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_template_utils_py.html#t12"><data value='TemplateUtils'>TemplateUtils</data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_template_utils_py.html">common/template_utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_template_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t42">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t42"><data value='RpcRequest'>RpcRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t49">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t49"><data value='RpcResponse'>RpcResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t56">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t56"><data value='RpcError'>RpcError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t63">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t63"><data value='HttpPayload'>HttpPayload</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t75">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t75"><data value='TestCase'>TestCase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t83">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t83"><data value='ErrorContext'>ErrorContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t98">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t98"><data value='UrlConfig'>UrlConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t105">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t105"><data value='ChainConfig'>ChainConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t116">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t116"><data value='Configurable'>Configurable</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t128">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t128"><data value='HttpClient'>HttpClient</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t136">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t136"><data value='AssertionUtils'>AssertionUtils</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t156">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t156"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t193">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t193"><data value='BlockIdentifier'>BlockIdentifier</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t198">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t198"><data value='TransactionHash'>TransactionHash</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t206">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t206"><data value='Address'>Address</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t215">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t215"><data value='EthereumBlock'>EthereumBlock</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t225">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t225"><data value='SolanaBlock'>SolanaBlock</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t234">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t234"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t246">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t246"><data value='LogEntry'>LogEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t257">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t257"><data value='TestResult'>TestResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t271">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t271"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t281">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html#t281"><data value='CacheConfig'>CacheConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html">common/types.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>147</td>
                <td>0</td>
                <td>21</td>
                <td class="right" data-ratio="147 147">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_utils_py.html#t26">common/utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_utils_py.html#t26"><data value='Utils'>Utils</data></a></td>
                <td>171</td>
                <td>75</td>
                <td>2</td>
                <td class="right" data-ratio="96 171">56.14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_utils_py.html">common/utils.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_utils_refactored_py.html#t22">common/utils_refactored.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_utils_refactored_py.html#t22"><data value='Utils'>Utils</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_utils_refactored_py.html">common/utils_refactored.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_utils_refactored_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bfc844598bd03e84_wrapper_py.html">common/wrapper.py</a></td>
                <td class="name left"><a href="z_bfc844598bd03e84_wrapper_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="13 16">81.25%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3311</td>
                <td>2124</td>
                <td>37</td>
                <td class="right" data-ratio="1187 3311">35.85%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-15 14:59 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
