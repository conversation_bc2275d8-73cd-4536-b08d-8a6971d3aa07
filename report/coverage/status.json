{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "7f090cafb17db1fcf46899f3030f5c6a", "files": {"z_1b48200c0765dce8___init___py": {"hash": "5fa274a9ed85eaf188d02b9a02234db1", "index": {"url": "z_1b48200c0765dce8___init___py.html", "file": "apis/__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_aptos_api_py": {"hash": "8cd69cfb4d8673fdebeedb63db1fe96a", "index": {"url": "z_1b48200c0765dce8_aptos_api_py.html", "file": "apis/aptos_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_base_api_py": {"hash": "c28ea7c03e068a2e198133bd0bb589ae", "index": {"url": "z_1b48200c0765dce8_base_api_py.html", "file": "apis/base_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_beacon_api_py": {"hash": "23a1634d440717b6e538d693ec51b3eb", "index": {"url": "z_1b48200c0765dce8_beacon_api_py.html", "file": "apis/beacon_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 123, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_cosmos_api_py": {"hash": "b26425211b8cd2882acfa3ea030b7fa2", "index": {"url": "z_1b48200c0765dce8_cosmos_api_py.html", "file": "apis/cosmos_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 458, "n_excluded": 0, "n_missing": 270, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_jsonrpc_api_py": {"hash": "cdec9c9c6807a959a6960b44da6a967a", "index": {"url": "z_1b48200c0765dce8_jsonrpc_api_py.html", "file": "apis/jsonrpc_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 252, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_linea_api_py": {"hash": "fef0581ed910921e2df91fc12bf4776a", "index": {"url": "z_1b48200c0765dce8_linea_api_py.html", "file": "apis/linea_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_near_api_py": {"hash": "78ae3136907268929e1c2570d346709d", "index": {"url": "z_1b48200c0765dce8_near_api_py.html", "file": "apis/near_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_solana_api_py": {"hash": "f6dd94cbe0e7378590daa8812c61ef3b", "index": {"url": "z_1b48200c0765dce8_solana_api_py.html", "file": "apis/solana_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 197, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_starknet_api_py": {"hash": "855bf5ebf24007c0392928e606b14411", "index": {"url": "z_1b48200c0765dce8_starknet_api_py.html", "file": "apis/starknet_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_sui_api_py": {"hash": "d3f44e431df6843390ded893d6e29ac0", "index": {"url": "z_1b48200c0765dce8_sui_api_py.html", "file": "apis/sui_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b48200c0765dce8_ton_api_py": {"hash": "1fa697fac9b84727595a1f73b745bd88", "index": {"url": "z_1b48200c0765dce8_ton_api_py.html", "file": "apis/ton_api.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84___init___py": {"hash": "5952fef8b4b7c52bfa1beacd967b7893", "index": {"url": "z_bfc844598bd03e84___init___py.html", "file": "common/__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_assertion_utils_py": {"hash": "c297d95af9f73635501da50fabb80f95", "index": {"url": "z_bfc844598bd03e84_assertion_utils_py.html", "file": "common/assertion_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 109, "n_excluded": 2, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_config_handler_py": {"hash": "0e33efa6b66e759a24dbd30ae90c3156", "index": {"url": "z_bfc844598bd03e84_config_handler_py.html", "file": "common/config_handler.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_config_manager_py": {"hash": "5c28c105fc697ce81aa4813c5271d367", "index": {"url": "z_bfc844598bd03e84_config_manager_py.html", "file": "common/config_manager.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_crypto_utils_py": {"hash": "198a734550cc115121d4600fb0ea5f08", "index": {"url": "z_bfc844598bd03e84_crypto_utils_py.html", "file": "common/crypto_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_enhanced_assertion_utils_py": {"hash": "4e0a4e787f89687cc8049deb0d721267", "index": {"url": "z_bfc844598bd03e84_enhanced_assertion_utils_py.html", "file": "common/enhanced_assertion_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_enhanced_config_manager_py": {"hash": "d413e60a5a102d4601ba93fd5314ab0b", "index": {"url": "z_bfc844598bd03e84_enhanced_config_manager_py.html", "file": "common/enhanced_config_manager.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 172, "n_excluded": 0, "n_missing": 172, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_error_config_loader_py": {"hash": "8a033f919be6bb1d0adad96c9bb55f6d", "index": {"url": "z_bfc844598bd03e84_error_config_loader_py.html", "file": "common/error_config_loader.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_error_reporter_py": {"hash": "13a94996766629de3f7f2c43a18b19cc", "index": {"url": "z_bfc844598bd03e84_error_reporter_py.html", "file": "common/error_reporter.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 170, "n_excluded": 0, "n_missing": 170, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_handle_path_py": {"hash": "5922164d1112207d3e26a1b3fb600c6c", "index": {"url": "z_bfc844598bd03e84_handle_path_py.html", "file": "common/handle_path.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_ip_mode_handler_py": {"hash": "c284f0c079bd36759c22f216dbabfd84", "index": {"url": "z_bfc844598bd03e84_ip_mode_handler_py.html", "file": "common/ip_mode_handler.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_network_client_py": {"hash": "1800a5d7f7998d9b9584c4e828cdaff3", "index": {"url": "z_bfc844598bd03e84_network_client_py.html", "file": "common/network_client.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_template_utils_py": {"hash": "b2a8b4f6a0819b45e3ec2867dec2ff13", "index": {"url": "z_bfc844598bd03e84_template_utils_py.html", "file": "common/template_utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_types_py": {"hash": "eff579a0440342c2cbad221d5c7c111d", "index": {"url": "z_bfc844598bd03e84_types_py.html", "file": "common/types.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 159, "n_excluded": 33, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_utils_py": {"hash": "e5001a56c833153dbee36ef30eed8db7", "index": {"url": "z_bfc844598bd03e84_utils_py.html", "file": "common/utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 217, "n_excluded": 2, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_utils_refactored_py": {"hash": "0eb5f945cf39cbe672cbd42901583da5", "index": {"url": "z_bfc844598bd03e84_utils_refactored_py.html", "file": "common/utils_refactored.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bfc844598bd03e84_wrapper_py": {"hash": "616a5a06cdd9e74b693c44eddb3730b3", "index": {"url": "z_bfc844598bd03e84_wrapper_py.html", "file": "common/wrapper.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}