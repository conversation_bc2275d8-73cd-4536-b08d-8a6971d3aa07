abstract:
  extends: evm_full
aptos:
  extends: aptos_base
arbitrum:
  extends: evm_archive
arbitrum_nova:
  extends: evm_archive
arbitrum_sepolia:
  extends: evm_archive
avalanche:
  extends: evm_archive
avalanche_fuji:
  extends: evm_full
base:
  extends: evm_archive
base_sepolia:
  extends: evm_full
beacon:
  extends: beacon_base
berachain:
  extends: evm_full
blast:
  extends: evm_archive
blast_sepolia:
  extends: evm_archive
bsc:
  extends: evm_archive
bsc_testnet:
  extends: evm_full
celo:
  extends: evm_full
conflux:
  extends: evm_archive
cosmos:
  extends: cosmos_rest
cronos:
  extends: evm_archive
eclipse:
  extends: solana_base
ethereum:
  extends: evm_archive
fantom:
  extends: evm_archive
gnosis:
  extends: evm_archive
holesky:
  extends: evm_full
hoodi:
  extends: evm_full
hyperliquid:
  extends: evm_full
ink:
  extends: evm_full
kaia:
  extends: evm_full
kaia_kairos:
  extends: evm_full
linea:
  extends: evm_archive
linea_sepolia:
  extends: evm_full
mantle:
  extends: evm_archive
merlin:
  extends: evm_full
merlin_testnet:
  extends: evm_full
meter:
  extends: evm_full
metis:
  extends: evm_full
monad_testnet:
  extends: evm_full
movement:
  extends: aptos_base
near:
  account_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - binancecold3.near
        block_id:
          __int__: $block
        changes_type: account_changes
  all_access_key_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - v2.ref-finance.near
        changes_type: all_access_key_changes
        finality: final
  block:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: block
      params:
        finality: final
  call_function:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: dev-*************
        args_base64: e30=
        finality: final
        method_name: get_num
        request_type: call_function
  changes_in_block:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes_in_block
      params:
        finality: final
  chunk:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: chunk
      params:
        block_id:
          __int__: $block
        shard_id: 0
  contract_code_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - priceoracle.near
        block_id:
          __int__: $block
        changes_type: contract_code_changes
  data_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - priceoracle.near
        block_id:
          __int__: $block
        changes_type: data_changes
        key_prefix_base64: ''
  gas_price:
  - expected: gas_price
    payload:
      id: 1
      jsonrpc: '2.0'
      method: gas_price
      params:
      - __int__: $block
  genesis_config:
  - expected: chain_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_genesis_config
  maintenance_windows:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_maintenance_windows
      params:
        account_id: binancecold3.near
  network_info:
  - expected: account_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: network_info
      params: []
  protocol_config:
  - expected: chain_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_protocol_config
      params:
        finality: final
  receipt:
  - expected: predecessor_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_receipt
      params:
        receipt_id: 86yAW29qcrTAgoC6fVx7BEpCRRozNRCUEdy6D55Mu7JZ
  single_access_key_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        changes_type: single_access_key_changes
        finality: final
        keys:
        - account_id: v2.ref-finance.near
          public_key: ed25519:25KEc7t7MQohAJ4EDThd2vkksKkwangnuJFzcoiXj9oM
  status:
  - expected: chain_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: status
      params: []
  tx:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: tx
      params:
        sender_account_id: hotwallet.kaiching
        tx_hash: G3tBSKbGxMobqieCamevw32DLojEH6oPfepoy69zkY38
        wait_until: EXECUTED
  tx_status:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_tx_status
      params:
        sender_account_id: hotwallet.kaiching
        tx_hash: G3tBSKbGxMobqieCamevw32DLojEH6oPfepoy69zkY38
        wait_until: EXECUTED
  validators:
  - expected: account_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: validators
      params:
      - null
  view_access_key:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: binancecold3.near
        finality: final
        public_key: ed25519:2RbBmPkcwAMEK3r7hMWc6gig3AoYUqqL5gSci9GU3p4J
        request_type: view_access_key
  view_access_key_list:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: binancecold3.near
        finality: final
        request_type: view_access_key_list
  view_account:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: binancecold3.near
        finality: final
        public_key: ed25519:2RbBmPkcwAMEK3r7hMWc6gig3AoYUqqL5gSci9GU3p4J
        request_type: view_access_key
  view_code:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: contract.main.burrow.near
        finality: final
        request_type: view_code
  view_state:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: priceoracle.near
        finality: final
        prefix_base64: ''
        request_type: view_state
near_testnet:
  account_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - price-oracle-bot-03.testnet
        block_id:
          __int__: $block
        changes_type: account_changes
  all_access_key_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - price-oracle-bot-03.testnet
        changes_type: all_access_key_changes
        finality: final
  block:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: block
      params:
        finality: final
  call_function:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: price-oracle-bot-03.testnet
        args_base64: e30=
        finality: final
        method_name: get_num
        request_type: call_function
  changes_in_block:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes_in_block
      params:
        finality: final
  chunk:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: chunk
      params:
        block_id:
          __int__: $block
        shard_id: 0
  contract_code_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - price-oracle-bot-03.testnet
        block_id:
          __int__: $block
        changes_type: contract_code_changes
  data_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        account_ids:
        - price-oracle-bot-03.testnet
        block_id:
          __int__: $block
        changes_type: data_changes
        key_prefix_base64: ''
  gas_price:
  - expected: gas_price
    payload:
      id: 1
      jsonrpc: '2.0'
      method: gas_price
      params:
      - __int__: $block
  genesis_config:
  - expected: chain_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_genesis_config
  maintenance_windows:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_maintenance_windows
      params:
        account_id: opengradient.testnet
  network_info:
  - expected: account_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: network_info
      params: []
  protocol_config:
  - expected: chain_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_protocol_config
      params:
        finality: final
  receipt:
  - expected: predecessor_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_receipt
      params:
        receipt_id: 8BtSicvQ9hKqW1vDfUsqf8tNmYKYnQWdz1nbrhjB6Lci
  single_access_key_changes:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_changes
      params:
        changes_type: single_access_key_changes
        finality: final
        keys:
        - account_id: price-oracle-bot-03.testnet
          public_key: ed25519:QFXNSDJYmUAyjXmqCSpDee7DkLxjk4Nx7hhK91qWD9j
  status:
  - expected: chain_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: status
      params: []
  tx:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: tx
      params:
        sender_account_id: opengradient.testnet
        tx_hash: 7y4Ct3ksCTr5PxbjCGTiBTaqeYkHSA95RTMkLTtzbXa5
        wait_until: EXECUTED
  tx_status:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: EXPERIMENTAL_tx_status
      params:
        sender_account_id: opengradient.testnet
        tx_hash: 7y4Ct3ksCTr5PxbjCGTiBTaqeYkHSA95RTMkLTtzbXa5
        wait_until: EXECUTED
  validators:
  - expected: account_id
    payload:
      id: 1
      jsonrpc: '2.0'
      method: validators
      params:
      - null
  view_access_key:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: price-oracle-bot-03.testnet
        finality: final
        public_key: ed25519:QFXNSDJYmUAyjXmqCSpDee7DkLxjk4Nx7hhK91qWD9j
        request_type: view_access_key
  view_access_key_list:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: price-oracle-bot-03.testnet
        finality: final
        request_type: view_access_key_list
  view_account:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: price-oracle-bot-03.testnet
        finality: final
        public_key: ed25519:QFXNSDJYmUAyjXmqCSpDee7DkLxjk4Nx7hhK91qWD9j
        request_type: view_access_key
  view_code:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: opengradient.testnet
        finality: final
        request_type: view_code
  view_state:
  - expected: block_hash
    payload:
      id: 1
      jsonrpc: '2.0'
      method: query
      params:
        account_id: price-oracle-bot-03.testnet
        finality: final
        prefix_base64: ''
        request_type: view_state
oasys:
  extends: evm_full
optimism:
  extends: evm_archive
optimism_sepolia:
  extends: evm_full
polygon:
  extends: evm_archive
polygon_amoy:
  extends: evm_full
polygon_zkevm:
  extends: evm_full
polygon_zkevm_cardona:
  extends: evm_full
scroll:
  extends: evm_full
scroll_sepolia:
  extends: evm_full
sei_evm:
  extends: evm_full
sei_evm_testnet:
  extends: evm_full
sepolia:
  extends: evm_full
sepolia_beacon:
  extends: beacon_base
solana:
  extends: solana_base
sonic:
  extends: evm_full
sonic_blaze:
  extends: evm_full
starknet:
  extends: starknet_base
story:
  extends: evm_full
sui:
  sui_get_chain_identifier:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getChainIdentifier
      params: []
  sui_get_checkpoint:
  - expected: epoch
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getCheckpoint
      params:
      - '10'
  sui_get_checkpoints:
  - expected: epoch
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getCheckpoints
      params:
      - '10'
      - 10
      - true
  sui_get_events:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getEvents
      params:
      - ASfo2kynAEgYZhhcnsC3nsvZaagSC7GPxDF4pdxJBWmB
  sui_get_latest_checkpoint_sequence_number:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getLatestCheckpointSequenceNumber
      params: []
  sui_get_move_function_arg_types:
  - expected: Object
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getMoveFunctionArgTypes
      params:
      - '0x0000000000000000000000000000000000000000000000000000000000000002'
      - bag
      - borrow
  sui_get_normalized_move_function:
  - expected: visibility
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getNormalizedMoveFunction
      params:
      - '0x0000000000000000000000000000000000000000000000000000000000000002'
      - bag
      - borrow
  sui_get_normalized_move_module:
  - expected: fileFormatVersion
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getNormalizedMoveModule
      params:
      - '0x0000000000000000000000000000000000000000000000000000000000000002'
      - bag
  sui_get_normalized_move_modules_by_package:
  - expected: fileFormatVersion
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getNormalizedMoveModulesByPackage
      params:
      - '0x0000000000000000000000000000000000000000000000000000000000000002'
  sui_get_normalized_move_struct:
  - expected: abilities
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getNormalizedMoveStruct
      params:
      - '0x0000000000000000000000000000000000000000000000000000000000000002'
      - bag
      - Bag
  sui_get_protocol_config:
  - expected: minSupportedProtocolVersion
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getProtocolConfig
      params: []
  sui_get_total_transaction_blocks:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getTotalTransactionBlocks
      params: []
  sui_get_transaction_block:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_getTransactionBlock
      params:
      - ASfo2kynAEgYZhhcnsC3nsvZaagSC7GPxDF4pdxJBWmB
      - showBalanceChanges: false
        showEffects: true
        showEvents: true
        showInput: true
        showObjectChanges: false
        showRawInput: false
  sui_multi_get_objects:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_multiGetObjects
      params:
      - - '0xce021583c14335a20a36dd10ab3714e8d0b835d0fd1dfed44150a1e7655a4b4b'
        - '0xac5bceec1b789ff840d7d4e6ce4ce61c90d190a7f8c4f4ddf0bff6ee2413c33c'
      - showBalanceChanges: false
        showEffects: true
        showEvents: true
        showInput: true
        showObjectChanges: false
        showRawInput: false
  sui_multi_get_transaction_blocks:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_multiGetTransactionBlocks
      params:
      - - Asx288EHxNCTZg4gAJb6kxtdbpZrdL2LaFnug2M6Ats
        - HrB8ka1pVyGYEHaXJ9D5HP9vzBTpWkr53L9PbjZrkcAf
      - showBalanceChanges: false
        showEffects: true
        showEvents: true
        showInput: true
        showObjectChanges: false
        showRawInput: false
  sui_try_multi_get_past_objects:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: sui_tryMultiGetPastObjects
      params:
      - - objectId: '0x125d701cc584f5aba71a1f73c52b0ef08493f1ac9801e8e6d9f0f5c1cc1bcfc4'
          version: '8241435'
      - showBcs: false
        showContent: true
        showDisplay: false
        showOwner: true
        showPreviousTransaction: true
        showStorageRebate: true
        showType: true
  suix_get_all_balances:
  - expected: coinType
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getAllBalances
      params:
      - '0xac5bceec1b789ff840d7d4e6ce4ce61c90d190a7f8c4f4ddf0bff6ee2413c33c'
      - '0x15610fa7ee546b96cb580be4060fae1c4bb15eca87f9a0aa931512bad445fc76'
      - '0x80841329787fd577639add61cc955ace969af60fadfb05b8ff752c2de4a8aa65'
  suix_get_all_coins:
  - expected: coinType
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getAllCoins
      params:
      - '0xac5bceec1b789ff840d7d4e6ce4ce61c90d190a7f8c4f4ddf0bff6ee2413c33c'
  suix_get_balance:
  - expected: coinType
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getBalance
      params:
      - '0x41f5975e3c6bd5c95f041a8493ad7e9934be26e69152d2c2e86d8a9bdbd242b3'
      - 0xa8816d3a6e3136e86bc2873b1f94a15cadc8af2703c075f2d546c2ae367f4df9::ocean::OCEAN
  suix_get_coin_metadata:
  - expected: OCEAN
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getCoinMetadata
      params:
      - 0xa8816d3a6e3136e86bc2873b1f94a15cadc8af2703c075f2d546c2ae367f4df9::ocean::OCEAN
  suix_get_coins:
  - expected: coinType
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getCoins
      params:
      - '0x10e5a7411aa6826e9fe1a3f358805d4fe91528cb6f031a7469f92b7a75a9a5f2'
  suix_get_latest_sui_system_state:
  - expected: epoch
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getLatestSuiSystemState
      params: []
  suix_get_reference_gas_price:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getReferenceGasPrice
      params: []
  suix_get_validators_apy:
  - expected: result
    payload:
      id: 1
      jsonrpc: '2.0'
      method: suix_getValidatorsApy
      params:
      - '0xac5bceec1b789ff840d7d4e6ce4ce61c90d190a7f8c4f4ddf0bff6ee2413c33c'
t3rn:
  extends: evm_full
taiko:
  extends: evm_full
taiko_hekla:
  extends: evm_full
ton:
  detectAddress:
  - expected: ok
    payload: /detectAddress?address=EQCvxJy4eG8hyHBFsZ7eePxrRsUQSFE_jpptRAYBmcG_DOGS
  getAddressBalance:
  - expected: ok
    payload: /getAddressBalance?address=EQAN58L9I7ciPE06FDjZlBkCvLahxJuM9U__pG2waWTBJs7v
  getAddressInformation:
  - expected: ok
    payload: /getAddressInformation?address=UQDJz-2XkfGYvbVNDZDnoCRusPQBoxTwA2qV5MkAhhlY4NAR
  getAddressState:
  - expected: ok
    payload: /getAddressState?address=EQAN58L9I7ciPE06FDjZlBkCvLahxJuM9U__pG2waWTBJs7v
  getConsensusBlock:
  - expected: ok
    payload: /getConsensusBlock
  getExtendedAddressInformation:
  - expected: ok
    payload: /getExtendedAddressInformation?address=UQA_Vz89BHqF5URBepSsjygEgGyOrWEQt42G4YsrOW3hry_G
  getMasterchainInfo:
  - expected: ok
    payload: /getMasterchainInfo
  getTokenData:
  - expected: ok
    payload: /getTokenData?address=EQCvxJy4eG8hyHBFsZ7eePxrRsUQSFE_jpptRAYBmcG_DOGS
  getTransactions:
  - expected: ok
    payload: /getTransactions?address=EQCvxJy4eG8hyHBFsZ7eePxrRsUQSFE_jpptRAYBmcG_DOGS&limit=1
  getWalletInformation:
  - expected: ok
    payload: /getWalletInformation?address=UQA_Vz89BHqF5URBepSsjygEgGyOrWEQt42G4YsrOW3hry_G
  packAddress:
  - expected: ok
    payload: /packAddress?address=EQCvxJy4eG8hyHBFsZ7eePxrRsUQSFE_jpptRAYBmcG_DOGS
  unpackAddress:
  - expected: ok
    payload: /unpackAddress?address=EQCD39VS5jcptHL8vMjEXrzGaRcCVYto7HUn4bpAOg8xqB2N
unichain:
  extends: evm_full
unichain_sepolia:
  extends: evm_full
viction:
  extends: evm_full
zeta:
  extends: cosmos_rest
zeta_athens:
  extends: cosmos_rest
zeta_athens_evm:
  extends: evm_full
zeta_evm:
  extends: evm_archive
zksync_era:
  extends: evm_full
zksync_era_sepolia:
  extends: evm_full
