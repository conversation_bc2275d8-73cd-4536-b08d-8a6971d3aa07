# 测试数据模板
# 定义通用的测试方法模板，减少重复

# ============ 基础模板 ============

base_jsonrpc:
  # 基础JSON-RPC模板
  _template_info:
    description: "基础JSON-RPC请求模板"
    version: "1.0"
  
  eth_chainId:
    - payload:
        jsonrpc: "2.0"
        method: "eth_chainId"
        params: []
        id: 1
      expected: "0x"

  eth_syncing:
    - payload:
        jsonrpc: "2.0"
        method: "eth_syncing"
        params: []
        id: 1

  eth_blockNumber:
    - payload:
        jsonrpc: "2.0"
        method: "eth_blockNumber"
        params: []
        id: 1
      expected: "0x"

  eth_gasPrice:
    - payload:
        jsonrpc: "2.0"
        method: "eth_gasPrice"
        params: []
        id: 1
      expected: "0x"

  net_version:
    - payload:
        jsonrpc: "2.0"
        method: "net_version"
        params: []
        id: 1

  net_listening:
    - payload:
        jsonrpc: "2.0"
        method: "net_listening"
        params: []
        id: 1

  web3_clientVersion:
    - payload:
        jsonrpc: "2.0"
        method: "web3_clientVersion"
        params: []
        id: 1

# ============ EVM兼容链模板 ============

evm_standard:
  extends: base_jsonrpc
  _template_info:
    description: "标准EVM兼容链模板"
    version: "1.0"
    inherits: ["base_jsonrpc"]

  eth_getBlockByNumber:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getBlockByNumber"
        params: ["latest", false]
        id: 1
      expected: "result"
    - payload:
        jsonrpc: "2.0"
        method: "eth_getBlockByNumber"
        params: ["safe", true]
        id: 1
      expected: "result"
    - payload:
        jsonrpc: "2.0"
        method: "eth_getBlockByNumber"
        params: ["finalized", false]
        id: 1
      expected: "result"

  eth_getBlockByHash:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getBlockByHash"
        params: [$blockhash, false]
        id: 1
      expected: "result"

  eth_getBalance:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getBalance"
        params: ["******************************************", "latest"]
        id: 1
      expected: "0x"

  eth_getTransactionByHash:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getTransactionByHash"
        params: [$transactionhash]
        id: 1
      expected: "result"

  eth_getTransactionReceipt:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getTransactionReceipt"
        params: [$transactionhash]
        id: 1
      expected: "result"

  eth_getTransactionCount:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getTransactionCount"
        params: ["******************************************", "latest"]
        id: 1
      expected: "0x"

  eth_call:
    - payload:
        jsonrpc: "2.0"
        method: "eth_call"
        params: [{"to": "******************************************", "data": "0x"}, "latest"]
        id: 1

  eth_getLogs:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getLogs"
        params: [{"fromBlock": "latest", "toBlock": "latest"}]
        id: 1
      expected: "result"

# ============ 存档节点模板 ============

evm_archive:
  extends: evm_standard
  _template_info:
    description: "EVM存档节点模板"
    version: "1.0"
    inherits: ["evm_standard"]

  archive:
    eth_getBlockByNumber:
      - payload:
          jsonrpc: "2.0"
          method: "eth_getBlockByNumber"
          params: ["0x1", false]
          id: 1
        expected: "result"
      - payload:
          jsonrpc: "2.0"
          method: "eth_getBlockByNumber"
          params: ["0x64", true]
          id: 1
        expected: "result"

    eth_getBalance:
      - payload:
          jsonrpc: "2.0"
          method: "eth_getBalance"
          params: ["******************************************", "0x1"]
          id: 1
        expected: "0x"

    eth_getTransactionCount:
      - payload:
          jsonrpc: "2.0"
          method: "eth_getTransactionCount"
          params: ["******************************************", "0x1"]
          id: 1
        expected: "0x"

    eth_call:
      - payload:
          jsonrpc: "2.0"
          method: "eth_call"
          params: [{"to": "******************************************", "data": "0x"}, "0x1"]
          id: 1

# ============ 过滤器模板 ============

evm_filters:
  _template_info:
    description: "EVM过滤器模板"
    version: "1.0"

  eth_newFilter:
    - payload:
        jsonrpc: "2.0"
        method: "eth_newFilter"
        params: [{"fromBlock": "latest", "toBlock": "latest"}]
        id: 1
      expected: "0x"

  eth_newBlockFilter:
    - payload:
        jsonrpc: "2.0"
        method: "eth_newBlockFilter"
        params: []
        id: 1
      expected: "0x"

  eth_newPendingTransactionFilter:
    - payload:
        jsonrpc: "2.0"
        method: "eth_newPendingTransactionFilter"
        params: []
        id: 1
      expected: "0x"

  eth_getFilterChanges:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getFilterChanges"
        params: [$filterid]
        id: 1
      expected: "result"

  eth_uninstallFilter:
    - payload:
        jsonrpc: "2.0"
        method: "eth_uninstallFilter"
        params: [$filterid]
        id: 1

# ============ 完整EVM模板 ============

evm_full:
  extends: [evm_standard, evm_filters]
  _template_info:
    description: "完整EVM功能模板"
    version: "1.0"
    inherits: ["evm_standard", "evm_filters"]

  eth_getBlockTransactionCountByHash:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getBlockTransactionCountByHash"
        params: [$blockhash]
        id: 1
      expected: "0x"

  eth_getBlockTransactionCountByNumber:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getBlockTransactionCountByNumber"
        params: ["latest"]
        id: 1
      expected: "0x"

  eth_getTransactionByBlockHashAndIndex:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getTransactionByBlockHashAndIndex"
        params: [$blockhash, "0x0"]
        id: 1
      expected: "result"

  eth_getTransactionByBlockNumberAndIndex:
    - payload:
        jsonrpc: "2.0"
        method: "eth_getTransactionByBlockNumberAndIndex"
        params: ["latest", "0x0"]
        id: 1
      expected: "result"

# ============ Solana模板 ============

solana_base:
  _template_info:
    description: "Solana基础模板"
    version: "1.0"

  getSlot:
    - payload:
        jsonrpc: "2.0"
        method: "getSlot"
        id: 1

  getVersion:
    - payload:
        jsonrpc: "2.0"
        method: "getVersion"
        id: 1

  getHealth:
    - payload:
        jsonrpc: "2.0"
        method: "getHealth"
        id: 1

  getBlockHeight:
    - payload:
        jsonrpc: "2.0"
        method: "getBlockHeight"
        id: 1

# ============ Starknet模板 ============

starknet_base:
  _template_info:
    description: "Starknet基础模板"
    version: "1.0"

  starknet_specVersion:
    - payload:
        jsonrpc: "2.0"
        method: "starknet_specVersion"
        params: []
        id: 1

  starknet_chainId:
    - payload:
        jsonrpc: "2.0"
        method: "starknet_chainId"
        params: []
        id: 1

  starknet_blockNumber:
    - payload:
        jsonrpc: "2.0"
        method: "starknet_blockNumber"
        params: []
        id: 1

# ============ Cosmos REST API模板 ============

cosmos_rest:
  _template_info:
    description: "Cosmos REST API模板"
    version: "1.0"

  rest_api:
    accounts:
      - payload: "/cosmos/auth/v1beta1/accounts"
        expected: "account_number"
    
    account_details:
      - payload: "/cosmos/auth/v1beta1/accounts/cosmos1fl48vsnmsdzcv85q5d2q4z5ajdha8yu34mf0eh"
        expected: "account_number"
    
    balances:
      - payload: "/cosmos/bank/v1beta1/balances/cosmos1fl48vsnmsdzcv85q5d2q4z5ajdha8yu34mf0eh"
        expected: "balances"

# ============ Aptos模板 ============

aptos_base:
  _template_info:
    description: "Aptos基础模板"
    version: "1.0"

  get_account:
    - payload: "/accounts/0x6de517a18f003625e7fba9b9dc29b310f2e3026bbeb1997b3ada9de1e3cec8d6"
      expected: "0x6de517a18f003625e7fba9b9dc29b310f2e3026bbeb1997b3ada9de1e3cec8d6"

  get_account_resources:
    - payload: "/accounts/0x6de517a18f003625e7fba9b9dc29b310f2e3026bbeb1997b3ada9de1e3cec8d6/resources"
      expected: "type"

# ============ Beacon Chain模板 ============

beacon_base:
  _template_info:
    description: "Beacon Chain基础模板"
    version: "1.0"

  genesis:
    - payload: "/eth/v1/beacon/genesis"
      expected: "genesis_time"

  block_root:
    - payload: "/eth/v1/beacon/blocks/genesis/root"
      expected: "data"
