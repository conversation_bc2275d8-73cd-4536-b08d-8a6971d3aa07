# 优化后的链配置文件
# 使用模板继承机制减少数据重复

# ============ 主网链 ============

ethereum:
  extends: evm_archive
  # 以太坊主网特有的测试用例可以在这里添加

arbitrum:
  extends: evm_archive
  # Arbitrum特有的测试用例

arbitrum_nova:
  extends: evm_archive

avalanche:
  extends: evm_archive

base:
  extends: evm_archive

blast:
  extends: evm_archive

bsc:
  extends: evm_archive

conflux:
  extends: evm_archive

cronos:
  extends: evm_archive

fantom:
  extends: evm_archive

gnosis:
  extends: evm_archive

linea:
  extends: evm_archive

mantle:
  extends: evm_archive

optimism:
  extends: evm_archive

polygon:
  extends: evm_archive

zeta_evm:
  extends: evm_archive

# ============ 测试网链 ============

arbitrum_sepolia:
  extends: evm_full

avalanche_fuji:
  extends: evm_full

base_sepolia:
  extends: evm_full

blast_sepolia:
  extends: evm_archive

bsc_testnet:
  extends: evm_full

holesky:
  extends: evm_full

linea_sepolia:
  extends: evm_full

optimism_sepolia:
  extends: evm_full

polygon_amoy:
  extends: evm_full

polygon_zkevm:
  extends: evm_full

polygon_zkevm_cardona:
  extends: evm_full

scroll:
  extends: evm_full

scroll_sepolia:
  extends: evm_full

sepolia:
  extends: evm_full

zksync_era:
  extends: evm_full

zksync_era_sepolia:
  extends: evm_full

# ============ 新兴链 ============

berachain:
  extends: evm_full

celo:
  extends: evm_full

hoodi:
  extends: evm_full

hyperliquid:
  extends: evm_full

ink:
  extends: evm_full

kaia:
  extends: evm_full

kaia_kairos:
  extends: evm_full

merlin:
  extends: evm_full

merlin_testnet:
  extends: evm_full

meter:
  extends: evm_full

metis:
  extends: evm_full

monad_testnet:
  extends: evm_full

oasys:
  extends: evm_full

sei_evm:
  extends: evm_full

sei_evm_testnet:
  extends: evm_full

sonic:
  extends: evm_full

sonic_blaze:
  extends: evm_full

story:
  extends: evm_full

taiko:
  extends: evm_full

taiko_hekla:
  extends: evm_full

unichain:
  extends: evm_full

unichain_sepolia:
  extends: evm_full

viction:
  extends: evm_full

zeta_athens_evm:
  extends: evm_full

# ============ 非EVM链 ============

solana:
  extends: solana_base
  # Solana特有的方法
  getAccountInfo:
    - payload:
        jsonrpc: "2.0"
        method: "getAccountInfo"
        params: ["********************************"]
        id: 1

  getBalance:
    - payload:
        jsonrpc: "2.0"
        method: "getBalance"
        params: ["********************************"]
        id: 1

eclipse:
  extends: solana_base
  # Eclipse特有的方法（如果有的话）

starknet:
  extends: starknet_base
  # Starknet特有的方法
  starknet_getBlockWithTxHashes:
    - payload:
        jsonrpc: "2.0"
        method: "starknet_getBlockWithTxHashes"
        params: ["latest"]
        id: 1

# ============ Cosmos生态链 ============

cosmos:
  extends: cosmos_rest

zeta:
  extends: cosmos_rest

zeta_athens:
  extends: cosmos_rest

# ============ Aptos生态链 ============

aptos:
  extends: aptos_base

movement:
  extends: aptos_base

# ============ Beacon Chain ============

beacon:
  extends: beacon_base

sepolia_beacon:
  extends: beacon_base

# ============ 其他链 ============

sui:
  # Sui有自己独特的API结构
  suix_get_all_balances:
    - payload:
        jsonrpc: "2.0"
        method: "suix_getAllBalances"
        params: ["0x0000000000000000000000000000000000000000000000000000000000000000"]
        id: 1

  suix_get_coins:
    - payload:
        jsonrpc: "2.0"
        method: "suix_getCoins"
        params: ["0x0000000000000000000000000000000000000000000000000000000000000000"]
        id: 1

near:
  # NEAR有自己的API结构
  view_access_key:
    - payload:
        jsonrpc: "2.0"
        id: 1
        method: "query"
        params:
          request_type: "view_access_key"
          finality: "final"
          account_id: "client.chainlink.testnet"
          public_key: "ed25519:H9k5eiU4xXS3M4z8HzKJSLaZdqGdGwBG49o7orNC4eZW"

near_testnet:
  # NEAR测试网
  view_access_key:
    - payload:
        jsonrpc: "2.0"
        id: 1
        method: "query"
        params:
          request_type: "view_access_key"
          finality: "final"
          account_id: "client.chainlink.testnet"
          public_key: "ed25519:H9k5eiU4xXS3M4z8HzKJSLaZdqGdGwBG49o7orNC4eZW"

ton:
  # TON有自己的REST API结构
  getAddressInformation:
    - payload: "/getAddressInformation?address=UQDJz-2XkfGYvbVNDZDnoCRusPQBoxTwA2qV5MkAhhlY4NAR"
      expected: "ok"

  getExtendedAddressInformation:
    - payload: "/getExtendedAddressInformation?address=UQA_Vz89BHqF5URBepSsjygEgGyOrWEQt42G4YsrOW3hry_G"
      expected: "ok"

# ============ 抽象测试数据 ============

abstract:
  extends: evm_full
  # 抽象测试数据，用于通用测试
