import pytest

if __name__ == '__main__':
    # 调用pytest库的主函数 main()执行所有的pytest测试用例，-v打印详细日志, -s打印输出日志，--alluredir用于指定存储测试结果的路径
    pytest.main(['-v', '-s'])

    # 启动 allure 服务，打开测试报告
    # os.system('allure serve ./report/json')

    # # 从./report/json目录将生成的报告输出到./report/html目录中,-c用于在生成新报告之前清理先前的报告
    # os.system('allure generate ./report/json -o ./report/html -c')
    # # 浏览器打开生成测试报告
    # os.system('allure open -h 127.0.0.1 -p 8883 ./report/html')

