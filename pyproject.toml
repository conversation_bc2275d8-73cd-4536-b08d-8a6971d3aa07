[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "blockpi-rpc-test"
description = "BlockPI RPC 测试框架"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "BlockPI Team", email = "<EMAIL>"},
]
keywords = ["blockchain", "rpc", "testing", "ethereum", "solana"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Testing",
    "Topic :: Internet :: WWW/HTTP",
]
dependencies = [
    "pytest>=7.4.0",
    "pytest-xdist>=3.3.0",
    "httpx>=0.25.0",
    "loguru>=0.7.0",
    "PyYAML>=6.0.1",
    "websocket-client>=1.6.0",
    "typing-extensions>=4.8.0",
]
dynamic = ["version"]

[project.optional-dependencies]
dev = [
    "mypy>=1.7.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "pytest-cov>=4.1.0",
    "pre-commit>=3.5.0",
]
docs = [
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/blockpi-network/rpc-test-framework"
Documentation = "https://blockpi-rpc-test.readthedocs.io/"
Repository = "https://github.com/blockpi-network/rpc-test-framework.git"
"Bug Tracker" = "https://github.com/blockpi-network/rpc-test-framework/issues"

[tool.setuptools_scm]
write_to = "common/_version.py"

[tool.mypy]
# MyPy 类型检查配置
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true

# 模块特定配置
[[tool.mypy.overrides]]
module = [
    "websocket.*",
    "yaml.*",
    "pytest.*",
    "loguru.*",
]
ignore_missing_imports = true

# 逐步启用严格模式的模块
[[tool.mypy.overrides]]
module = [
    "common.types",
    "common.error_reporter",
    "common.enhanced_assertion_utils",
    "common.test_decorators",
]
strict = true

[tool.black]
# Black 代码格式化配置
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | logs
  | report
)/
'''

[tool.isort]
# isort 导入排序配置
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["common", "apis", "testcases"]
known_third_party = ["pytest", "httpx", "loguru", "yaml", "websocket"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.flake8]
# Flake8 代码检查配置
max-line-length = 100
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
    ".venv",
    ".mypy_cache",
    "logs",
    "report",
]
per-file-ignores = [
    "__init__.py:F401",  # imported but unused
    "conftest.py:F401,F811",  # redefinition of unused
]

[tool.pytest.ini_options]
# Pytest 配置
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
]
testpaths = ["testcases"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "repeat: 重复执行用例",
    "evm: EVM链相关用例",
    "archive: 归档节点相关用例",
    # 具体链标记
    "abstract: marks tests for Abstract",
    "aptos: marks tests for Aptos",
    "arbitrum: marks tests for Arbitrum",
    "arbitrum_nova: marks tests for Arbitrum Nova",
    "arbitrum_sepolia: marks tests for Arbitrum Sepolia",
    "avalanche: marks tests for Avalanche",
    "avalanche_fuji: marks tests for Avalanche Fuji",
    "base: marks tests for Base",
    "base_sepolia: marks tests for Base Sepolia",
    "beacon: marks tests for Beacon",
    "berachain: marks tests for Berachain",
    "blast: marks tests for Blast",
    "blast_sepolia: marks tests for Blast Sepolia",
    "bsc: marks tests for BSC",
    "bsc_testnet: marks tests for BSC Testnet",
    "celo: marks tests for Celo",
    "conflux: marks tests for Conflux",
    "cosmos: marks tests for Cosmos ecosystem",
    "cronos: marks tests for Cronos",
    "eclipse: marks tests for Eclipse",
    "ethereum: marks tests for Ethereum",
    "fantom: marks tests for Fantom",
    "gnosis: marks tests for Gnosis",
    "holesky: marks tests for Holesky",
    "hoodi: marks tests for Hoodi",
    "hyperliquid: marks tests for Hyperliquid",
    "ink: marks tests for Ink",
    "kaia: marks tests for Kaia",
    "kaia_kairos: marks tests for Kaia Kairos",
    "linea: marks tests for Linea",
    "linea_sepolia: marks tests for Linea Sepolia",
    "mantle: marks tests for Mantle",
    "merlin: marks tests for Merlin",
    "merlin_testnet: marks tests for Merlin Testnet",
    "meter: marks tests for Meter",
    "metis: marks tests for Metis",
    "monad_testnet: marks tests for Monad Testnet",
    "movement: marks tests for Movement",
    "near: marks tests for Near",
    "near_testnet: marks tests for Near Testnet",
    "oasys: marks tests for Oasys",
    "optimism: marks tests for Optimism",
    "optimism_sepolia: marks tests for Optimism Sepolia",
    "polygon: marks tests for Polygon",
    "polygon_amoy: marks tests for Polygon Amoy",
    "polygon_zkevm: marks tests for Polygon zkEVM",
    "polygon_zkevm_cardona: marks tests for Polygon zkEVM Cardona",
    "scroll: marks tests for Scroll",
    "scroll_sepolia: marks tests for Scroll Sepolia",
    "sei_evm: marks tests for Sei EVM",
    "sei_evm_testnet: marks tests for Sei EVM Testnet",
    "sepolia: marks tests for Sepolia",
    "sepolia_beacon: marks tests for Sepolia Beacon",
    "solana: marks tests for Solana",
    "sonic: marks tests for Sonic",
    "sonic_blaze: marks tests for Sonic Blaze",
    "starknet: marks tests for Starknet",
    "story: marks tests for Story",
    "sui: marks tests for Sui",
    "t3rn: marks tests for T3rn",
    "taiko: marks tests for Taiko",
    "taiko_hekla: marks tests for Taiko Hekla",
    "tomo: marks tests for Tomo",
    "ton: marks tests for TON",
    "unichain: marks tests for Unichain",
    "unichain_sepolia: marks tests for Unichain Sepolia",
    "viction: marks tests for Viction",
    "zeta: marks tests for Zeta",
    "zeta_evm: marks tests for Zeta EVM",
    "zeta_testnet: marks tests for Zeta Testnet",
    "zeta_testnet_evm: marks tests for Zeta Testnet EVM",
    "zksync_era: marks tests for zkSync Era",
    "zksync_era_sepolia: marks tests for zkSync Era Sepolia",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
# Coverage 配置
source = ["common", "apis"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/.*",
    "setup.py",
    "conftest.py",
]

[tool.coverage.report]
# Coverage 报告配置
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "report/coverage"
